import cv2
import numpy as np
import os
import time
import threading
from datetime import datetime
from flask import current_app
import requests
from requests.exceptions import RequestException

class Camera:
    def __init__(self, camera_id, url):
        self.camera_id = camera_id
        self.url = url
        self.cap = None
        self.connected = False
        self.last_frame = None
        self.last_frame_time = None
        self.running = False
        self.lock = threading.Lock()
        self.connection_attempts = 0
        self.max_connection_attempts = 5
    
    def connect(self):
        """Connect to the camera"""
        try:
            self.cap = cv2.VideoCapture(self.url)
            if self.cap.isOpened():
                self.connected = True
                self.connection_attempts = 0
                return True
            else:
                self.connected = False
                self.connection_attempts += 1
                return False
        except Exception as e:
            print(f"Error connecting to camera {self.camera_id}: {str(e)}")
            self.connected = False
            self.connection_attempts += 1
            return False
    
    def disconnect(self):
        """Disconnect from the camera"""
        if self.cap and self.connected:
            self.cap.release()
        self.connected = False
        self.cap = None
    
    def get_frame(self):
        """Get a single frame from the camera"""
        if not self.connected:
            if self.connection_attempts < self.max_connection_attempts:
                self.connect()
            if not self.connected:
                return None
        
        try:
            ret, frame = self.cap.read()
            if ret:
                self.last_frame = frame
                self.last_frame_time = datetime.now()
                return frame
            else:
                self.disconnect()
                return None
        except Exception as e:
            print(f"Error getting frame from camera {self.camera_id}: {str(e)}")
            self.disconnect()
            return None
    
    def get_jpeg(self):
        """Get a JPEG encoded frame"""
        frame = self.get_frame()
        if frame is not None:
            ret, jpeg = cv2.imencode('.jpg', frame)
            if ret:
                return jpeg.tobytes()
        return None
    
    def start_capture(self):
        """Start continuous capture in a separate thread"""
        if self.running:
            return
        
        self.running = True
        threading.Thread(target=self._capture_loop, daemon=True).start()
    
    def stop_capture(self):
        """Stop continuous capture"""
        self.running = False
    
    def _capture_loop(self):
        """Continuous capture loop"""
        while self.running:
            with self.lock:
                self.get_frame()
            time.sleep(0.03)  # ~30 FPS
    
    def save_image(self, session_id=None):
        """Save the current frame to disk"""
        with self.lock:
            if self.last_frame is None:
                return None
            
            # Create directory structure
            storage_path = current_app.config['IMAGE_STORAGE_PATH']
            date_str = datetime.now().strftime('%Y%m%d')
            time_str = datetime.now().strftime('%H%M%S')
            
            if session_id:
                dir_path = os.path.join(storage_path, f'session_{session_id}')
            else:
                dir_path = os.path.join(storage_path, date_str)
            
            os.makedirs(dir_path, exist_ok=True)
            
            # Save image
            filename = f'camera_{self.camera_id}_{time_str}.jpg'
            file_path = os.path.join(dir_path, filename)
            
            cv2.imwrite(file_path, self.last_frame)
            
            # Return relative path for database storage
            rel_path = os.path.join('images/shots', os.path.relpath(file_path, storage_path))
            return rel_path.replace('\\', '/')

class CameraManager:
    def __init__(self, app=None):
        self.cameras = {}
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        camera_urls = app.config['CAMERA_URLS']
        
        for i, url in enumerate(camera_urls):
            camera_id = i + 1
            self.cameras[camera_id] = Camera(camera_id, url)
    
    def get_camera(self, camera_id):
        """Get camera by ID"""
        return self.cameras.get(camera_id)
    
    def get_all_cameras(self):
        """Get all cameras"""
        return self.cameras
    
    def start_all_cameras(self):
        """Start capture on all cameras"""
        for camera in self.cameras.values():
            camera.start_capture()
    
    def stop_all_cameras(self):
        """Stop capture on all cameras"""
        for camera in self.cameras.values():
            camera.stop_capture()
    
    def get_camera_status(self):
        """Get status of all cameras"""
        status = {}
        for camera_id, camera in self.cameras.items():
            status[camera_id] = {
                'connected': camera.connected,
                'url': camera.url,
                'last_frame_time': camera.last_frame_time
            }
        return status

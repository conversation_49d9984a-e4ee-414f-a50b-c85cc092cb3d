#!/usr/bin/env python3
"""
Test script to verify camera settings functionality
"""

from app import create_app
from app.camera import CameraManager
import os

def test_camera_settings():
    """Test camera settings loading and saving"""
    
    # Create app instance
    app = create_app()
    
    with app.app_context():
        print("=== Camera Settings Test ===")
        print()
        
        # Test 1: Check current configuration
        print("1. Current Camera Configuration:")
        print(f"   CAMERA_COUNT: {app.config['CAMERA_COUNT']}")
        print(f"   CAMERA_URLS: {app.config['CAMERA_URLS']}")
        print()
        
        # Test 2: Initialize camera manager
        print("2. Initializing Camera Manager...")
        camera_manager = CameraManager()
        camera_manager.init_app(app)
        print(f"   Cameras initialized: {len(camera_manager.cameras)}")
        print()
        
        # Test 3: Check camera status
        print("3. Camera Status:")
        camera_status = camera_manager.get_camera_status()
        for camera_id, status in camera_status.items():
            print(f"   Camera {camera_id}:")
            print(f"     URL: {status['url']}")
            print(f"     Connected: {status['connected']}")
            print(f"     Last Frame Time: {status['last_frame_time']}")
        print()
        
        # Test 4: Check .env file
        print("4. Current .env file content:")
        env_path = '.env'
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                if line.startswith('CAMERA_'):
                    print(f"   {line.strip()}")
        else:
            print("   .env file not found!")
        print()
        
        # Test 5: Test camera connection (Camera 1)
        print("5. Testing Camera 1 Connection:")
        camera_1 = camera_manager.get_camera(1)
        if camera_1:
            print(f"   Camera 1 URL: {camera_1.url}")
            try:
                # Try to connect
                if camera_1.connect():
                    print("   ✓ Connection successful!")
                    # Try to get a frame
                    frame = camera_1.get_frame()
                    if frame is not None:
                        print("   ✓ Frame capture successful!")
                        print(f"   Frame shape: {frame.shape}")
                    else:
                        print("   ✗ Frame capture failed!")
                    camera_1.disconnect()
                else:
                    print("   ✗ Connection failed!")
            except Exception as e:
                print(f"   ✗ Error: {str(e)}")
        else:
            print("   ✗ Camera 1 not found!")
        print()
        
        print("=== Test Complete ===")

if __name__ == "__main__":
    test_camera_settings()

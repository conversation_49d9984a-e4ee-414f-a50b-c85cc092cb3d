{% extends "base.html" %}

{% block title %}Home - Armed Forces Shooting Range{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 text-center mb-5">
        <div class="military-badge">OFFICIAL MILITARY TRAINING SYSTEM</div>
        <h1 class="display-4">ARMED FORCES SHOOTING RANGE</h1>
        <p class="lead">Advanced tactical shooting analysis and performance tracking system</p>
        <div class="military-divider"></div>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-video-camera fa-4x mb-3 text-primary"></i>
                <h3 class="card-title">TACTICAL SURVEILLANCE</h3>
                <p class="card-text">Military-grade IP camera system with 8+ camera support for comprehensive range monitoring.</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-crosshairs fa-4x mb-3 text-danger"></i>
                <h3 class="card-title">PRECISION ANALYSIS</h3>
                <p class="card-text">Advanced ballistic analysis with military-spec accuracy for tactical shooting improvement.</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-medal fa-4x mb-3 text-success"></i>
                <h3 class="card-title">COMBAT READINESS</h3>
                <p class="card-text">Track personnel performance, weapon proficiency, and combat readiness metrics.</p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12 text-center">
        <div class="military-divider"></div>
        {% if current_user.is_authenticated %}
        <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-shield-alt me-2"></i> COMMAND CENTER
        </a>
        {% else %}
        <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-lg me-3">
            <i class="fas fa-user-shield me-2"></i> SECURE LOGIN
        </a>
        <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary btn-lg">
            <i class="fas fa-user-plus me-2"></i> REGISTER
        </a>
        {% endif %}
    </div>
</div>
{% endblock %}

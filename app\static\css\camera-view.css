/* Camera View Styles */

/* Camera Grid Layout */
.camera-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

/* Camera Card */
.camera-card {
    height: 100%;
    transition: transform 0.2s ease;
    margin-bottom: 0;
}

.camera-card:hover {
    transform: translateY(-3px);
}

.camera-card .card-header {
    padding: 0.5rem 1rem;
}

.camera-card .card-body {
    padding: 0;
    position: relative;
    overflow: hidden;
    aspect-ratio: 4/3;
    background-color: #000;
}

/* Camera Feed */
.camera-feed {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Camera Controls */
.camera-controls {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.camera-controls .btn {
    flex: 1;
    max-width: 120px;
}

/* Camera Assignment */
.camera-assignment {
    margin-top: 10px;
}

.camera-assignment-form .input-group {
    width: 100%;
}

/* Fullscreen View */
.fullscreen-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1050;
    display: none;
    justify-content: center;
    align-items: center;
}

.fullscreen-view img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
}

.close-fullscreen {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    z-index: 1051;
}

/* List View */
.list-view {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.list-view .camera-card {
    width: 100%;
}

.list-view .camera-card .card-body {
    aspect-ratio: 16/9;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .camera-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .camera-card .card-header h5 {
        font-size: 0.9rem;
    }
    
    .camera-controls {
        flex-direction: column;
    }
    
    .camera-controls .btn {
        max-width: 100%;
    }
}

/* Camera Status Indicators */
.camera-status {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 0.8rem;
}

.camera-status.online {
    background-color: rgba(40, 167, 69, 0.7);
}

.camera-status.offline {
    background-color: rgba(220, 53, 69, 0.7);
}

/* Camera Placeholder */
.camera-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: #f8f9fa;
    color: #6c757d;
    font-size: 1.2rem;
    text-align: center;
    padding: 20px;
}

.camera-placeholder i {
    font-size: 3rem;
    margin-bottom: 10px;
}

{% extends "base.html" %}

{% block title %}Surveillance System - Armed Forces Shooting Range{% endblock %}

{% block styles %}
<style>
    .camera-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 12px;
    }

    .camera-feed-container {
        position: relative;
        width: 100%;
        height: 200px;
        background-color: #f8f9fa;
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .camera-feed {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        border: none;
        border-radius: 5px;
        transition: opacity 0.3s ease;
        display: block;
    }

    .camera-feed.offline-feed {
        opacity: 0.7;
        filter: grayscale(100%);
    }

    .camera-loading {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(248, 249, 250, 0.9);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    .camera-loading.hidden {
        display: none;
    }



    .live-indicator {
        position: absolute;
        top: 5px;
        left: 5px;
        background-color: rgba(0, 0, 0, 0.8);
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 0.6rem;
        font-weight: bold;
        z-index: 20;
        display: flex;
        align-items: center;
        gap: 2px;
    }

    .live-indicator i {
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .camera-controls {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .camera-controls .btn {
        font-size: 0.75rem;
        padding: 6px 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-radius: 4px;
    }

    .camera-controls .btn i {
        margin-right: 4px;
    }

    /* Responsive design for smaller screens */
    @media (max-width: 768px) {
        .camera-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 10px;
        }

        .camera-feed-container {
            height: 150px;
        }

        .camera-controls {
            grid-template-columns: 1fr;
            gap: 5px;
        }

        .camera-controls .btn {
            font-size: 0.7rem;
            padding: 4px 6px;
        }

        .live-indicator {
            font-size: 0.5rem;
            padding: 1px 4px;
        }
    }

    @media (max-width: 480px) {
        .camera-grid {
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .camera-feed-container {
            height: 120px;
        }
    }

    .camera-card {
        transition: all 0.3s ease;
    }

    .camera-card:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .camera-card .card-body {
        padding: 0;
        margin: 0;
    }

    .fullscreen-view {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        z-index: 1000;
        display: none;
        justify-content: center;
        align-items: center;
    }

    .fullscreen-view img {
        max-width: 90%;
        max-height: 90%;
    }

    .close-fullscreen {
        position: absolute;
        top: 20px;
        right: 20px;
        color: white;
        font-size: 30px;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="military-badge">SECURE SURVEILLANCE</div>
        <h1>TACTICAL CAMERA SYSTEM</h1>
        <p class="lead">Monitor all surveillance cameras in the shooting range</p>
        <div class="military-divider"></div>
    </div>
    <div class="col-md-4 text-end">
        <div class="d-flex justify-content-end gap-2">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="grid-view-btn">
                    <i class="fas fa-th"></i> Grid View
                </button>
                <button type="button" class="btn btn-outline-primary" id="list-view-btn">
                    <i class="fas fa-list"></i> List View
                </button>
            </div>
            <a href="{{ url_for('camera_bp.camera_settings') }}" class="btn btn-outline-success">
                <i class="fas fa-cog"></i> Camera Settings
            </a>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        {% endif %}
        {% endwith %}
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">SURVEILLANCE FEEDS</h5>
            </div>
            <div class="card-body">
                <div class="camera-grid" id="camera-container">
                    {% for i in range(1, 9) %}
                    <div class="camera-card card">
                        <div class="card-header {% if i in camera_assignments %}bg-success text-white{% endif %}">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-video me-1"></i> CAMERA {{ i }}
                                </h5>
                                {% if i in camera_assignments %}
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user-check me-1"></i>
                                    <span class="small">{{ camera_assignments[i].full_name }}</span>
                                </div>
                                {% else %}
                                <span class="badge bg-secondary">UNASSIGNED</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Camera snapshot display with fallback to default image -->
                            <div class="camera-feed-container">
                                <img src="{{ url_for('static', filename='images/cameras/default-camera.jpg') }}"
                                     class="camera-feed"
                                     alt="Camera {{ i }}"
                                     data-camera-id="{{ i }}"
                                     id="camera-image-{{ i }}">

                                <!-- Loading indicator -->
                                <div class="camera-loading hidden" id="loading-{{ i }}">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Connecting to camera...</p>
                                </div>

                                <!-- Live indicator -->
                                <div class="live-indicator hidden" id="live-{{ i }}">
                                    <i class="fas fa-circle text-danger"></i>
                                    <span class="text-white small">LIVE</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="camera-controls mb-2">
                                <button type="button" class="btn btn-sm btn-warning live-preview-btn" data-camera-id="{{ i }}">
                                    <i class="fas fa-video"></i> LIVE PREVIEW
                                </button>
                                <button type="button" class="btn btn-sm btn-secondary test-connection-btn" data-camera-id="{{ i }}">
                                    <i class="fas fa-plug"></i> TEST CONNECTION
                                </button>
                                <button type="button" class="btn btn-sm btn-primary fullscreen-btn" data-camera-id="{{ i }}">
                                    <i class="fas fa-expand"></i> EXPAND
                                </button>
                                <button type="button" class="btn btn-sm btn-success capture-btn" data-camera-id="{{ i }}">
                                    <i class="fas fa-camera"></i> CAPTURE
                                </button>
                            </div>

                            <div class="camera-assignment mt-2">
                                <form action="{{ url_for('camera_bp.assign_shooter') }}" method="post" class="camera-assignment-form">
                                    <input type="hidden" name="camera_id" value="{{ i }}">
                                    <div class="input-group input-group-sm">
                                        <select class="form-select form-select-sm" name="shooter_id" aria-label="Assign shooter">
                                            <option value="">Unassign camera</option>
                                            {% for shooter in shooters %}
                                            <option value="{{ shooter.id }}" {% if i in camera_assignments and camera_assignments[i].id == shooter.id %}selected{% endif %}>
                                                {{ shooter.full_name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                        <button type="submit" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-link"></i> ASSIGN
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="fullscreen-view" id="fullscreen-view">
    <div class="close-fullscreen" id="close-fullscreen">
        <i class="fas fa-times"></i>
    </div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="text-white mb-3">TACTICAL SURVEILLANCE</h2>
                <img id="fullscreen-image" src="" alt="Fullscreen camera view" class="img-fluid">
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize camera interface
        initializeCameraInterface();

        // Grid/List view toggle
        $('#grid-view-btn').click(function() {
            $('#camera-container').removeClass('list-view').addClass('camera-grid');
        });

        $('#list-view-btn').click(function() {
            $('#camera-container').removeClass('camera-grid').addClass('list-view');
            $('.camera-card').css('width', '100%');
        });

        // Fullscreen view
        $('.fullscreen-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            const cameraImage = $(`#camera-image-${cameraId}`);
            const src = cameraImage.attr('src');
            const cameraTitle = $(this).closest('.camera-card').find('.card-header h5').text().trim();

            $('#fullscreen-image').attr('src', src);
            $('#fullscreen-view h2').text('TACTICAL SURVEILLANCE - ' + cameraTitle);
            $('#fullscreen-view').css('display', 'flex');

            // If camera is in live mode, start fullscreen updates
            const liveIndicator = $(`#live-${cameraId}`);
            if (!liveIndicator.hasClass('hidden')) {
                startFullscreenUpdates(cameraId);
            }

            // Add keyboard event listener for ESC key
            $(document).on('keydown.fullscreen', function(e) {
                if (e.key === 'Escape') {
                    closeFullscreen();
                }
            });
        });

        $('#close-fullscreen').click(function() {
            closeFullscreen();
        });

        function closeFullscreen() {
            $('#fullscreen-view').css('display', 'none');
            $(document).off('keydown.fullscreen');

            // Stop fullscreen updates
            stopFullscreenUpdates();
        }

        // Fullscreen update system
        let fullscreenInterval = null;
        let fullscreenCameraId = null;

        function startFullscreenUpdates(cameraId) {
            stopFullscreenUpdates();
            fullscreenCameraId = cameraId;

            fullscreenInterval = setInterval(function() {
                updateFullscreenSnapshot();
            }, 250);

            // Initial update
            updateFullscreenSnapshot();
        }

        function stopFullscreenUpdates() {
            if (fullscreenInterval) {
                clearInterval(fullscreenInterval);
                fullscreenInterval = null;
                fullscreenCameraId = null;
            }
        }

        function updateFullscreenSnapshot() {
            if (!fullscreenCameraId) return;

            const timestamp = new Date().getTime();
            const snapshotUrl = `/camera/snapshot/${fullscreenCameraId}?t=${timestamp}`;

            // Update fullscreen image
            $('#fullscreen-image').attr('src', snapshotUrl);
        }

        // Live Preview button - Snapshot-based system
        $('.live-preview-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            const button = $(this);
            const cameraImage = $(`#camera-image-${cameraId}`);
            const loadingIndicator = $(`#loading-${cameraId}`);
            const liveIndicator = $(`#live-${cameraId}`);

            // Toggle button state
            if (button.hasClass('btn-warning')) {
                // Start live preview
                button.removeClass('btn-warning').addClass('btn-danger');
                button.html('<i class="fas fa-stop"></i> STOP PREVIEW');

                // Show loading indicator
                loadingIndicator.removeClass('hidden');

                // Start snapshot updates
                startSnapshotUpdates(cameraId);

                // Show live indicator
                liveIndicator.removeClass('hidden');

                // Hide loading after initial connection attempt
                setTimeout(function() {
                    loadingIndicator.addClass('hidden');
                }, 2000);

            } else {
                // Stop live preview
                button.removeClass('btn-danger').addClass('btn-warning');
                button.html('<i class="fas fa-video"></i> LIVE PREVIEW');

                // Stop snapshot updates
                stopSnapshotUpdates(cameraId);

                // Hide live indicator
                liveIndicator.addClass('hidden');

                // Revert to default image
                cameraImage.attr('src', '/static/images/cameras/default-camera.jpg');
                cameraImage.removeClass('offline-feed');
            }
        });

        // Test Connection button
        $('.test-connection-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            const button = $(this);

            // Update button state
            button.prop('disabled', true);
            button.html('<i class="fas fa-spinner fa-spin"></i> TESTING...');

            // Test the connection
            $.ajax({
                url: `/camera/test/${cameraId}`,
                type: 'GET',
                timeout: 10000, // 10 second timeout
                success: function(data) {
                    if (data.success) {
                        // Show success message
                        showToast('success', `Camera ${cameraId} connection successful!`);
                    } else {
                        // Show error message
                        showToast('error', `Camera ${cameraId} connection failed: ${data.error}`);
                    }
                },
                error: function() {
                    // Show error message
                    showToast('error', `Camera ${cameraId} connection test failed!`);
                },
                complete: function() {
                    // Reset button state
                    button.prop('disabled', false);
                    button.html('<i class="fas fa-plug"></i> TEST CONNECTION');
                }
            });
        });

        // Capture image
        $('.capture-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            // This is a placeholder - in a real session, you'd have a session ID
            const sessionId = 1;

            $.get(`/camera/capture/${cameraId}/${sessionId}`, function(data) {
                if (data.success) {
                    showToast('success', `Image captured successfully! Shot ID: ${data.shot_id}`);
                } else {
                    showToast('error', `Failed to capture image: ${data.error}`);
                }
            });
        });

        // Initialize camera interface
        function initializeCameraInterface() {
            // Hide loading indicators after a delay
            setTimeout(function() {
                $('.camera-loading').addClass('hidden');
            }, 1000);
        }

        // Snapshot update system
        const snapshotIntervals = {};

        function startSnapshotUpdates(cameraId) {
            // Clear any existing interval
            stopSnapshotUpdates(cameraId);

            // Start new interval for 0.25 second updates (250ms)
            snapshotIntervals[cameraId] = setInterval(function() {
                updateCameraSnapshot(cameraId);
            }, 250);

            // Initial snapshot
            updateCameraSnapshot(cameraId);
        }

        function stopSnapshotUpdates(cameraId) {
            if (snapshotIntervals[cameraId]) {
                clearInterval(snapshotIntervals[cameraId]);
                delete snapshotIntervals[cameraId];
            }
        }

        function updateCameraSnapshot(cameraId) {
            const cameraImage = $(`#camera-image-${cameraId}`);

            // Add timestamp to prevent caching
            const timestamp = new Date().getTime();
            const snapshotUrl = `/camera/snapshot/${cameraId}?t=${timestamp}`;

            // Create a new image to test loading
            const testImage = new Image();

            testImage.onload = function() {
                // Successfully loaded, update the display
                cameraImage.attr('src', snapshotUrl);
                cameraImage.removeClass('offline-feed');
            };

            testImage.onerror = function() {
                // Failed to load, keep showing default image
                cameraImage.addClass('offline-feed');
            };

            // Start loading the test image
            testImage.src = snapshotUrl;
        }

        // Toast notification function
        function showToast(type, message) {
            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
            const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

            const toastHtml = `
                <div class="toast ${toastClass} text-white" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header ${toastClass} text-white border-0">
                        <i class="fas ${iconClass} me-2"></i>
                        <strong class="me-auto">${type === 'success' ? 'Success' : 'Error'}</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            // Add toast container if it doesn't exist
            if ($('#toast-container').length === 0) {
                $('body').append('<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 11000;"></div>');
            }

            // Add toast to container
            $('#toast-container').append(toastHtml);

            // Show toast
            const toastElement = new bootstrap.Toast(document.getElementById(toastId));
            toastElement.show();

            // Remove toast element after it's hidden
            document.getElementById(toastId).addEventListener('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    });
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Surveillance System - Armed Forces Shooting Range{% endblock %}

{% block styles %}
<style>
    .camera-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 15px;
    }

    .camera-feed-container {
        position: relative;
        width: 100%;
        height: 200px;
        background-color: #f8f9fa;
        border-radius: 5px;
        overflow: hidden;
    }

    .camera-feed {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 5px;
        border: 1px solid #ddd;
        transition: opacity 0.3s ease;
    }

    .camera-feed.offline-feed {
        opacity: 0.7;
        filter: grayscale(100%);
    }

    .camera-loading {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(248, 249, 250, 0.9);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    .camera-loading.hidden {
        display: none;
    }

    .camera-status {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: bold;
        text-transform: uppercase;
        z-index: 20;
    }

    .camera-status.online {
        background-color: rgba(40, 167, 69, 0.9);
        color: white;
    }

    .camera-status.offline {
        background-color: rgba(220, 53, 69, 0.9);
        color: white;
    }

    .camera-status.checking {
        background-color: rgba(255, 193, 7, 0.9);
        color: black;
    }

    .camera-controls {
        margin-top: 10px;
    }

    .camera-card {
        transition: all 0.3s ease;
    }

    .camera-card:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .fullscreen-view {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        z-index: 1000;
        display: none;
        justify-content: center;
        align-items: center;
    }

    .fullscreen-view img {
        max-width: 90%;
        max-height: 90%;
    }

    .close-fullscreen {
        position: absolute;
        top: 20px;
        right: 20px;
        color: white;
        font-size: 30px;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="military-badge">SECURE SURVEILLANCE</div>
        <h1>TACTICAL CAMERA SYSTEM</h1>
        <p class="lead">Monitor all surveillance cameras in the shooting range</p>
        <div class="military-divider"></div>
    </div>
    <div class="col-md-4 text-end">
        <div class="d-flex justify-content-end gap-2">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="grid-view-btn">
                    <i class="fas fa-th"></i> Grid View
                </button>
                <button type="button" class="btn btn-outline-primary" id="list-view-btn">
                    <i class="fas fa-list"></i> List View
                </button>
            </div>
            <a href="{{ url_for('camera_bp.camera_settings') }}" class="btn btn-outline-success">
                <i class="fas fa-cog"></i> Camera Settings
            </a>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        {% endif %}
        {% endwith %}
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">SURVEILLANCE FEEDS</h5>
            </div>
            <div class="card-body">
                <div class="camera-grid" id="camera-container">
                    {% for i in range(1, 9) %}
                    <div class="camera-card card">
                        <div class="card-header {% if i in camera_assignments %}bg-success text-white{% endif %}">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-video me-1"></i> CAMERA {{ i }}
                                </h5>
                                {% if i in camera_assignments %}
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user-check me-1"></i>
                                    <span class="small">{{ camera_assignments[i].full_name }}</span>
                                </div>
                                {% else %}
                                <span class="badge bg-secondary">UNASSIGNED</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Live camera feed with fallback to default image -->
                            <div class="camera-feed-container">
                                <img src="{{ url_for('camera_bp.video_feed', camera_id=i) }}"
                                     class="camera-feed"
                                     alt="Camera {{ i }}"
                                     data-camera-id="{{ i }}"
                                     onerror="this.src='{{ url_for('static', filename='images/cameras/default-camera.jpg') }}'; this.classList.add('offline-feed');">

                                <!-- Loading indicator -->
                                <div class="camera-loading" id="loading-{{ i }}">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Connecting to camera...</p>
                                </div>
                            </div>

                            <!-- Camera status indicator -->
                            <div class="camera-status" id="status-{{ i }}">
                                <i class="fas fa-circle me-1"></i> <span class="status-text">CHECKING...</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="camera-controls mb-2">
                                <button type="button" class="btn btn-sm btn-warning live-preview-btn" data-camera-id="{{ i }}">
                                    <i class="fas fa-video"></i> LIVE PREVIEW
                                </button>
                                <button type="button" class="btn btn-sm btn-secondary test-connection-btn" data-camera-id="{{ i }}">
                                    <i class="fas fa-plug"></i> TEST CONNECTION
                                </button>
                                <button type="button" class="btn btn-sm btn-primary fullscreen-btn" data-camera-id="{{ i }}">
                                    <i class="fas fa-expand"></i> EXPAND
                                </button>
                                <button type="button" class="btn btn-sm btn-success capture-btn" data-camera-id="{{ i }}">
                                    <i class="fas fa-camera"></i> CAPTURE
                                </button>
                            </div>

                            <div class="camera-assignment mt-2">
                                <form action="{{ url_for('camera_bp.assign_shooter') }}" method="post" class="camera-assignment-form">
                                    <input type="hidden" name="camera_id" value="{{ i }}">
                                    <div class="input-group input-group-sm">
                                        <select class="form-select form-select-sm" name="shooter_id" aria-label="Assign shooter">
                                            <option value="">Unassign camera</option>
                                            {% for shooter in shooters %}
                                            <option value="{{ shooter.id }}" {% if i in camera_assignments and camera_assignments[i].id == shooter.id %}selected{% endif %}>
                                                {{ shooter.full_name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                        <button type="submit" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-link"></i> ASSIGN
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="fullscreen-view" id="fullscreen-view">
    <div class="close-fullscreen" id="close-fullscreen">
        <i class="fas fa-times"></i>
    </div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="text-white mb-3">TACTICAL SURVEILLANCE</h2>
                <img id="fullscreen-image" src="" alt="Fullscreen camera view" class="img-fluid">
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize camera status checking
        initializeCameraStatus();

        // Grid/List view toggle
        $('#grid-view-btn').click(function() {
            $('#camera-container').removeClass('list-view').addClass('camera-grid');
        });

        $('#list-view-btn').click(function() {
            $('#camera-container').removeClass('camera-grid').addClass('list-view');
            $('.camera-card').css('width', '100%');
        });

        // Fullscreen view
        $('.fullscreen-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            const src = $(this).closest('.camera-card').find('.camera-feed').attr('src');
            const cameraTitle = $(this).closest('.camera-card').find('.card-header h5').text().trim();

            $('#fullscreen-image').attr('src', src);
            $('#fullscreen-view h2').text('TACTICAL SURVEILLANCE - ' + cameraTitle);
            $('#fullscreen-view').css('display', 'flex');

            // Add keyboard event listener for ESC key
            $(document).on('keydown.fullscreen', function(e) {
                if (e.key === 'Escape') {
                    closeFullscreen();
                }
            });
        });

        $('#close-fullscreen').click(function() {
            closeFullscreen();
        });

        function closeFullscreen() {
            $('#fullscreen-view').css('display', 'none');
            $(document).off('keydown.fullscreen');
        }

        // Live Preview button
        $('.live-preview-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            const button = $(this);
            const cameraFeed = $(this).closest('.camera-card').find('.camera-feed');
            const loadingIndicator = $(this).closest('.camera-card').find('.camera-loading');

            // Toggle button state
            if (button.hasClass('btn-warning')) {
                // Start live preview
                button.removeClass('btn-warning').addClass('btn-danger');
                button.html('<i class="fas fa-stop"></i> STOP PREVIEW');

                // Show loading indicator
                loadingIndicator.removeClass('hidden');

                // Update the image source to the live stream
                const streamUrl = `/camera/stream/${cameraId}`;
                cameraFeed.attr('src', streamUrl);

                // Hide loading after a delay
                setTimeout(function() {
                    loadingIndicator.addClass('hidden');
                }, 3000);

            } else {
                // Stop live preview
                button.removeClass('btn-danger').addClass('btn-warning');
                button.html('<i class="fas fa-video"></i> LIVE PREVIEW');

                // Revert to default image
                cameraFeed.attr('src', '/static/images/cameras/default-camera.jpg');
                cameraFeed.removeClass('offline-feed');
            }
        });

        // Test Connection button
        $('.test-connection-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            const button = $(this);
            const statusElement = $('#status-' + cameraId);
            const statusText = statusElement.find('.status-text');

            // Update button state
            button.prop('disabled', true);
            button.html('<i class="fas fa-spinner fa-spin"></i> TESTING...');

            // Update status to checking
            statusElement.removeClass('online offline').addClass('checking');
            statusText.text('TESTING...');

            // Test the connection
            $.ajax({
                url: `/camera/test/${cameraId}`,
                type: 'GET',
                timeout: 10000, // 10 second timeout
                success: function(data) {
                    if (data.success) {
                        statusElement.removeClass('checking offline').addClass('online');
                        statusText.text('ONLINE');

                        // Show success message
                        showToast('success', `Camera ${cameraId} connection successful!`);
                    } else {
                        statusElement.removeClass('checking online').addClass('offline');
                        statusText.text('OFFLINE');

                        // Show error message
                        showToast('error', `Camera ${cameraId} connection failed: ${data.error}`);
                    }
                },
                error: function() {
                    statusElement.removeClass('checking online').addClass('offline');
                    statusText.text('OFFLINE');

                    // Show error message
                    showToast('error', `Camera ${cameraId} connection test failed!`);
                },
                complete: function() {
                    // Reset button state
                    button.prop('disabled', false);
                    button.html('<i class="fas fa-plug"></i> TEST CONNECTION');
                }
            });
        });

        // Capture image
        $('.capture-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            // This is a placeholder - in a real session, you'd have a session ID
            const sessionId = 1;

            $.get(`/camera/capture/${cameraId}/${sessionId}`, function(data) {
                if (data.success) {
                    showToast('success', `Image captured successfully! Shot ID: ${data.shot_id}`);
                } else {
                    showToast('error', `Failed to capture image: ${data.error}`);
                }
            });
        });

        // Camera status and loading management
        function initializeCameraStatus() {
            // Hide loading indicators after a delay and check camera status
            setTimeout(function() {
                $('.camera-loading').addClass('hidden');
                checkCameraStatus();
            }, 2000);

            // Set up periodic status checking
            setInterval(checkCameraStatus, 30000); // Check every 30 seconds
        }

        function checkCameraStatus() {
            $.ajax({
                url: '/camera/status',
                type: 'GET',
                success: function(data) {
                    updateCameraStatus(data);
                },
                error: function() {
                    console.log('Failed to get camera status');
                }
            });
        }

        function updateCameraStatus(statusData) {
            for (let cameraId in statusData) {
                const status = statusData[cameraId];
                const statusElement = $('#status-' + cameraId);
                const statusText = statusElement.find('.status-text');

                // Update status indicator
                statusElement.removeClass('online offline checking');

                if (status.connected) {
                    statusElement.addClass('online');
                    statusText.text('ONLINE');
                } else {
                    statusElement.addClass('offline');
                    statusText.text('OFFLINE');
                }
            }
        }

        // Handle camera feed loading errors
        $('.camera-feed').on('load', function() {
            const cameraId = $(this).data('camera-id');
            $('#loading-' + cameraId).addClass('hidden');

            // Update status to online if this is a live stream
            if ($(this).attr('src').includes('/camera/stream/')) {
                const statusElement = $('#status-' + cameraId);
                statusElement.removeClass('offline checking').addClass('online');
                statusElement.find('.status-text').text('ONLINE');
            }
        });

        $('.camera-feed').on('error', function() {
            const cameraId = $(this).data('camera-id');
            $('#loading-' + cameraId).addClass('hidden');

            // Update status to offline
            const statusElement = $('#status-' + cameraId);
            statusElement.removeClass('online checking').addClass('offline');
            statusElement.find('.status-text').text('OFFLINE');
        });

        // Toast notification function
        function showToast(type, message) {
            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
            const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

            const toastHtml = `
                <div class="toast ${toastClass} text-white" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header ${toastClass} text-white border-0">
                        <i class="fas ${iconClass} me-2"></i>
                        <strong class="me-auto">${type === 'success' ? 'Success' : 'Error'}</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            // Add toast container if it doesn't exist
            if ($('#toast-container').length === 0) {
                $('body').append('<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 11000;"></div>');
            }

            // Add toast to container
            $('#toast-container').append(toastHtml);

            // Show toast
            const toastElement = new bootstrap.Toast(document.getElementById(toastId));
            toastElement.show();

            // Remove toast element after it's hidden
            document.getElementById(toastId).addEventListener('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    });
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Start Mission - Armed Forces Shooting Range{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="military-badge">MISSION CONTROL</div>
        <h1>START NEW TRAINING MISSION</h1>
        <p class="lead">Set up a new shooting training session for individual or unit</p>
        <div class="military-divider"></div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">MISSION PARAMETERS</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('main.start_session') }}" method="post">
                    <div class="mb-3">
                        <label class="form-label">Session Type</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="session_type" id="individual_session" value="individual" checked>
                            <label class="form-check-label" for="individual_session">
                                Individual Training
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="session_type" id="group_session" value="group">
                            <label class="form-check-label" for="group_session">
                                Unit Training (All members)
                            </label>
                        </div>
                    </div>

                    <div id="individual-selection" class="mb-3">
                        <label for="shooter_id" class="form-label">Individual Soldier</label>
                        <select class="form-select" id="shooter_id" name="shooter_id">
                            <option value="" selected disabled>Select soldier</option>
                            {% for shooter in shooters %}
                            <option value="{{ shooter.id }}">{{ shooter.full_name }}</option>
                            {% endfor %}
                        </select>
                        {% if not shooters %}
                        <div class="form-text text-danger">
                            No soldiers available. <a href="{{ url_for('main.add_shooter') }}">Add a soldier</a> first.
                        </div>
                        {% endif %}
                    </div>

                    <div id="group-selection" class="mb-3">
                        <label for="group_id" class="form-label">Combat Unit</label>
                        <select class="form-select" id="group_id" name="group_id">
                            <option value="" selected disabled>Select unit</option>
                            {% for group in groups %}
                            <option value="{{ group.id }}">{{ group.name }} ({{ group.shooters.count() }} members)</option>
                            {% endfor %}
                        </select>
                        {% if not groups %}
                        <div class="form-text text-danger">
                            No units available. Add a unit first.
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="weapon_id" class="form-label">Weapon</label>
                        <select class="form-select" id="weapon_id" name="weapon_id" required>
                            <option value="" selected disabled>Select weapon</option>
                            {% for weapon in weapons %}
                            <option value="{{ weapon.id }}">{{ weapon.name }} ({{ weapon.caliber }})</option>
                            {% endfor %}
                        </select>
                        {% if not weapons %}
                        <div class="form-text text-danger">
                            No weapons available. <a href="{{ url_for('main.add_weapon') }}">Add a weapon</a> first.
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="distance" class="form-label">Distance (meters)</label>
                        <input type="number" class="form-control" id="distance" name="distance" min="1" required>
                    </div>

                    <div class="mb-3">
                        <label for="target_type" class="form-label">Target Type</label>
                        <select class="form-select" id="target_type" name="target_type" required>
                            <option value="" selected disabled>Select target type</option>
                            <option value="Standard">Standard</option>
                            <option value="Bullseye">Bullseye</option>
                            <option value="Silhouette">Silhouette</option>
                            <option value="Custom">Custom</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="weather_conditions" class="form-label">Weather Conditions</label>
                        <input type="text" class="form-control" id="weather_conditions" name="weather_conditions">
                        <div class="form-text">Optional: Describe weather conditions (temperature, wind, etc.)</div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        <div class="form-text">Optional: Any additional notes about this session</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('main.sessions') }}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary" {% if not shooters or not weapons %}disabled{% endif %}>
                            <i class="fas fa-flag-checkered me-2"></i> DEPLOY MISSION
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the radio buttons and selection divs
        const individualRadio = document.getElementById('individual_session');
        const groupRadio = document.getElementById('group_session');
        const individualSelection = document.getElementById('individual-selection');
        const groupSelection = document.getElementById('group-selection');

        // Add event listeners to the radio buttons
        individualRadio.addEventListener('change', function() {
            if (this.checked) {
                individualSelection.style.display = 'block';
                groupSelection.style.display = 'none';

                // Make shooter_id required and group_id not required
                document.getElementById('shooter_id').setAttribute('required', '');
                document.getElementById('group_id').removeAttribute('required');
            }
        });

        groupRadio.addEventListener('change', function() {
            if (this.checked) {
                individualSelection.style.display = 'none';
                groupSelection.style.display = 'block';

                // Make group_id required and shooter_id not required
                document.getElementById('group_id').setAttribute('required', '');
                document.getElementById('shooter_id').removeAttribute('required');
            }
        });
    });
</script>
{% endblock %}
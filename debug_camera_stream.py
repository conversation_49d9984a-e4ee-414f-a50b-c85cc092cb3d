#!/usr/bin/env python3
"""
Debug script to test camera streaming functionality
"""

from app import create_app
from app.camera import CameraManager
import cv2
import os

def test_camera_stream():
    """Test camera streaming functionality"""
    
    # Create app instance
    app = create_app()
    
    with app.app_context():
        print("=== Camera Stream Debug ===")
        print()
        
        # Initialize camera manager
        camera_manager = CameraManager()
        camera_manager.init_app(app)
        
        # Test Camera 1
        print("Testing Camera 1...")
        camera_1 = camera_manager.get_camera(1)
        
        if camera_1:
            print(f"Camera 1 URL: {camera_1.url}")
            print(f"Camera 1 Connected: {camera_1.connected}")
            
            # Try to connect
            if camera_1.connect():
                print("✓ Camera 1 connected successfully!")
                
                # Try to get a frame
                frame = camera_1.get_frame()
                if frame is not None:
                    print(f"✓ Frame captured! Shape: {frame.shape}")
                    
                    # Try to get JPEG
                    jpeg_data = camera_1.get_jpeg()
                    if jpeg_data:
                        print(f"✓ JPEG data generated! Size: {len(jpeg_data)} bytes")
                        
                        # Save a test image
                        test_dir = os.path.join('app', 'static', 'images', 'test')
                        os.makedirs(test_dir, exist_ok=True)
                        
                        test_image_path = os.path.join(test_dir, 'camera_1_test.jpg')
                        with open(test_image_path, 'wb') as f:
                            f.write(jpeg_data)
                        print(f"✓ Test image saved to: {test_image_path}")
                        
                    else:
                        print("✗ Failed to generate JPEG data")
                else:
                    print("✗ Failed to capture frame")
                
                camera_1.disconnect()
            else:
                print("✗ Failed to connect to Camera 1")
        else:
            print("✗ Camera 1 not found")
        
        print()
        print("=== Debug Complete ===")

if __name__ == "__main__":
    test_camera_stream()

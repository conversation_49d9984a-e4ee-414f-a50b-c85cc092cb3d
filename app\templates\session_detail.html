{% extends "base.html" %}

{% block title %}Mission Details - Armed Forces Shooting Range{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="military-badge">MISSION RECORD</div>
        <h1>TRAINING SESSION #{{ session.id }}</h1>
        <p class="lead">{{ session.date.strftime('%Y-%m-%d %H:%M') }}</p>
        <div class="military-divider"></div>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('main.sessions') }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left"></i> BACK TO MISSIONS
        </a>
        <button type="button" class="btn btn-primary" id="captureBtn">
            <i class="fas fa-camera"></i> CAPTURE SHOT
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Session Information</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>Shooter:</th>
                        <td>
                            <a href="{{ url_for('main.shooter', id=session.shooter.id) }}">
                                {{ session.shooter.full_name }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>Weapon:</th>
                        <td>{{ session.weapon.name }} ({{ session.weapon.caliber }})</td>
                    </tr>
                    <tr>
                        <th>Distance:</th>
                        <td>{{ session.distance }} meters</td>
                    </tr>
                    <tr>
                        <th>Target Type:</th>
                        <td>{{ session.target_type }}</td>
                    </tr>
                    <tr>
                        <th>Weather:</th>
                        <td>{{ session.weather_conditions or 'Not specified' }}</td>
                    </tr>
                    <tr>
                        <th>Date:</th>
                        <td>{{ session.date.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <th>Shots:</th>
                        <td>{{ shots|length }}</td>
                    </tr>
                </table>

                {% if session.notes %}
                <div class="mt-3">
                    <h6>Notes:</h6>
                    <p>{{ session.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">SURVEILLANCE SELECTION</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="cameraSelect" class="form-label">Select Camera</label>
                    <select class="form-select" id="cameraSelect">
                        {% if session.shooter.camera_id %}
                        <option value="{{ session.shooter.camera_id }}" selected>Camera {{ session.shooter.camera_id }} (Assigned)</option>
                        {% endif %}
                        {% for i in range(1, 9) %}
                        {% if i != session.shooter.camera_id %}
                        <option value="{{ i }}">Camera {{ i }}</option>
                        {% endif %}
                        {% endfor %}
                    </select>
                    {% if session.shooter.camera_id %}
                    <div class="form-text text-success">
                        <i class="fas fa-check-circle"></i> This soldier has an assigned camera
                    </div>
                    {% else %}
                    <div class="form-text text-warning">
                        <i class="fas fa-exclamation-triangle"></i> No camera assigned to this soldier
                    </div>
                    {% endif %}
                </div>
                <div class="d-grid">
                    <button type="button" class="btn btn-primary" id="viewCameraBtn">
                        <i class="fas fa-eye"></i> VIEW CAMERA
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Live Camera View</h5>
            </div>
            <div class="card-body p-0">
                <div class="text-center p-3" id="cameraPlaceholder">
                    <p class="text-muted">Select a camera to view the live feed</p>
                </div>
                <div id="cameraView">
                    <img src="" id="cameraFeed" class="img-fluid w-100" alt="Camera Feed">
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-primary" id="captureFromViewBtn" disabled>
                        <i class="fas fa-camera"></i> CAPTURE SHOT
                    </button>
                    <button type="button" class="btn btn-secondary" id="fullscreenBtn" disabled>
                        <i class="fas fa-expand"></i> FULLSCREEN
                    </button>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">Shot History</h5>
            </div>
            <div class="card-body">
                {% if shots %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Time</th>
                                <th>Camera</th>
                                <th>Score</th>
                                <th>Image</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for shot in shots %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ shot.timestamp.strftime('%H:%M:%S') }}</td>
                                <td>Camera {{ shot.camera_id }}</td>
                                <td>
                                    {% if shot.score %}
                                    {{ shot.score }}
                                    {% else %}
                                    <span class="badge bg-secondary">Not scored</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if shot.image_path %}
                                    <button type="button" class="btn btn-sm btn-info view-shot-btn" data-shot-id="{{ shot.id }}">
                                        <i class="fas fa-eye"></i> VIEW
                                    </button>
                                    {% else %}
                                    <span class="badge bg-danger">No image</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No shots recorded yet. Use the camera controls to capture shots.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Shot Details Modal -->
<div class="modal fade" id="shotDetailsModal" tabindex="-1" aria-labelledby="shotDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shotDetailsModalLabel">Shot Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="shotImage" class="text-center mb-3">
                    <img src="" class="img-fluid" alt="Shot Image">
                </div>
                <div id="shotDetails">
                    <!-- Shot details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="analyzeBtn">Analyze Shot</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Auto-load the assigned camera if available
        {% if session.shooter.camera_id %}
        // Automatically load the assigned camera
        const assignedCameraId = {{ session.shooter.camera_id }};
        $('#cameraPlaceholder').hide();
        $('#cameraView').show();
        $('#cameraFeed').attr('src', "{{ url_for('camera_bp.video_feed', camera_id=1) }}".replace('1', assignedCameraId));
        $('#captureFromViewBtn').prop('disabled', false);
        $('#fullscreenBtn').prop('disabled', false);
        {% endif %}

        // Handle camera selection
        $('#viewCameraBtn').click(function() {
            const cameraId = $('#cameraSelect').val();
            $('#cameraPlaceholder').hide();
            $('#cameraView').show();
            $('#cameraFeed').attr('src', "{{ url_for('camera_bp.video_feed', camera_id=1) }}".replace('1', cameraId));
            $('#captureFromViewBtn').prop('disabled', false);
            $('#fullscreenBtn').prop('disabled', false);
        });

        // Handle shot capture
        $('#captureFromViewBtn, #captureBtn').click(function() {
            const cameraId = $('#cameraSelect').val();
            const sessionId = {{ session.id }};

            $.get(`/camera/capture/${cameraId}/${sessionId}`, function(data) {
                if (data.success) {
                    alert('Shot captured successfully!');
                    // Reload the page to show the new shot
                    location.reload();
                } else {
                    alert(`Failed to capture shot: ${data.error}`);
                }
            });
        });

        // Handle shot view
        $('.view-shot-btn').click(function() {
            const shotId = $(this).data('shot-id');
            // In a real application, you would load shot details via AJAX
            // For now, we'll just show the modal with placeholder content
            $('#shotDetailsModal').modal('show');
        });

        // Handle fullscreen
        $('#fullscreenBtn').click(function() {
            const elem = document.getElementById('cameraFeed');
            if (elem.requestFullscreen) {
                elem.requestFullscreen();
            } else if (elem.webkitRequestFullscreen) { /* Safari */
                elem.webkitRequestFullscreen();
            } else if (elem.msRequestFullscreen) { /* IE11 */
                elem.msRequestFullscreen();
            }
        });
    });
</script>
{% endblock %}

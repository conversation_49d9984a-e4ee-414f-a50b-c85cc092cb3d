"""Add army_number to Shooter model

Revision ID: 16b4192e8ef5
Revises: ea77cbec7ba7
Create Date: 2025-05-17 17:53:46.755771

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '16b4192e8ef5'
down_revision = 'ea77cbec7ba7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('shooter', schema=None) as batch_op:
        batch_op.add_column(sa.Column('army_number', sa.String(length=20), nullable=True))
        batch_op.create_index(batch_op.f('ix_shooter_army_number'), ['army_number'], unique=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('shooter', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_shooter_army_number'))
        batch_op.drop_column('army_number')

    # ### end Alembic commands ###

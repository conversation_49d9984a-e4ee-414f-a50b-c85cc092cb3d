<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Display Test</title>
    <style>
        .camera-feed-container {
            position: relative;
            width: 100%;
            height: 200px;
            background-color: #f8f9fa;
            border-radius: 5px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #007bff;
        }

        .camera-feed {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            border: none;
            border-radius: 5px;
            display: block;
        }

        .test-container {
            max-width: 300px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .test-title {
            text-align: center;
            margin-bottom: 20px;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h3 class="test-title">Camera Display Test</h3>
        <div class="camera-feed-container">
            <img src="app/static/images/cameras/default-camera.jpg" 
                 class="camera-feed" 
                 alt="Test Camera"
                 id="test-camera">
        </div>
        <p style="text-align: center; margin-top: 10px; font-family: Arial, sans-serif; font-size: 12px;">
            This should show the default camera image fitting perfectly in the container without black borders.
        </p>
    </div>

    <script>
        // Test different image sources
        const testImage = document.getElementById('test-camera');
        
        testImage.onerror = function() {
            console.log('Image failed to load, trying alternative path...');
            this.src = 'static/images/cameras/default-camera.jpg';
        };
        
        testImage.onload = function() {
            console.log('Image loaded successfully');
            console.log('Image dimensions:', this.naturalWidth, 'x', this.naturalHeight);
            console.log('Container dimensions:', this.offsetWidth, 'x', this.offsetHeight);
        };
    </script>
</body>
</html>

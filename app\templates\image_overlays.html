{% extends "base.html" %}

{% block title %}Image Overlay Settings - Armed Forces Shooting Range{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-colorpicker@3.4.0/dist/css/bootstrap-colorpicker.min.css">
<style>
    .overlay-preview {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 20px;
        background-color: #f8f9fa;
        position: relative;
    }
    
    .overlay-preview img {
        max-width: 100%;
        border-radius: 3px;
    }
    
    .preview-loader {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }
    
    .config-card {
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-bottom: 15px;
        transition: all 0.3s;
    }
    
    .config-card:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .config-card.active {
        border-color: var(--military-dark-green);
        box-shadow: 0 0 0 3px rgba(26, 58, 26, 0.2);
    }
    
    .config-card .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .form-section {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    
    .form-section:last-child {
        border-bottom: none;
    }
    
    .form-section-title {
        margin-bottom: 15px;
        font-weight: 600;
        color: var(--military-dark-green);
    }
    
    .colorpicker-input-addon {
        width: 40px;
    }
    
    .marker-preview {
        width: 50px;
        height: 50px;
        border: 1px solid #ddd;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="military-badge">IMAGE PROCESSING</div>
        <h1>IMAGE OVERLAY SETTINGS</h1>
        <p class="lead">Configure how information is displayed on target images</p>
        <div class="military-divider"></div>
    </div>
    <div class="col-md-4 text-end">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addConfigModal">
            <i class="fas fa-plus me-2"></i> NEW CONFIGURATION
        </button>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        {% endif %}
        {% endwith %}
    </div>
</div>

<div class="row">
    <!-- Configuration List -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">SAVED CONFIGURATIONS</h5>
            </div>
            <div class="card-body">
                {% if configs %}
                <div class="list-group">
                    {% for config in configs %}
                    <div class="config-card card mb-2 {% if config.id == active_config.id %}active{% endif %}">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">{{ config.name }}</h6>
                            {% if config.is_default %}
                            <span class="badge bg-success">Default</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <p class="card-text small">
                                <strong>Font:</strong> {{ config.font_family }}, {{ config.font_size }}px<br>
                                <strong>Position:</strong> {{ config.text_position.replace('-', ' ').title() }}
                            </p>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('main.edit_overlay_config', id=config.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                {% if not config.is_default %}
                                <button type="button" class="btn btn-sm btn-outline-danger delete-config-btn" 
                                        data-bs-toggle="modal" data-bs-target="#deleteConfigModal" 
                                        data-config-id="{{ config.id }}" 
                                        data-config-name="{{ config.name }}">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No configurations found. Create a new one to get started.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Configuration Editor -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">EDIT CONFIGURATION: {{ active_config.name }}</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('main.update_overlay_config', id=active_config.id) }}" method="post">
                    <!-- Preview Section -->
                    <div class="overlay-preview mb-4">
                        <img src="{{ url_for('static', filename=preview_image) }}" alt="Preview" id="previewImage">
                        <div class="preview-loader" id="previewLoader" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Basic Settings -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-cog me-2"></i> BASIC SETTINGS
                        </h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Configuration Name</label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ active_config.name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="is_default" class="form-label">Default Configuration</label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="is_default" name="is_default" 
                                           {% if active_config.is_default %}checked{% endif %}>
                                    <label class="form-check-label" for="is_default">Set as default</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Text Settings -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-font me-2"></i> TEXT SETTINGS
                        </h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="font_family" class="form-label">Font Family</label>
                                <select class="form-select" id="font_family" name="font_family">
                                    <option value="Arial" {% if active_config.font_family == 'Arial' %}selected{% endif %}>Arial</option>
                                    <option value="Verdana" {% if active_config.font_family == 'Verdana' %}selected{% endif %}>Verdana</option>
                                    <option value="Tahoma" {% if active_config.font_family == 'Tahoma' %}selected{% endif %}>Tahoma</option>
                                    <option value="Times New Roman" {% if active_config.font_family == 'Times New Roman' %}selected{% endif %}>Times New Roman</option>
                                    <option value="Courier New" {% if active_config.font_family == 'Courier New' %}selected{% endif %}>Courier New</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="font_size" class="form-label">Font Size (px)</label>
                                <input type="number" class="form-control" id="font_size" name="font_size" 
                                       value="{{ active_config.font_size }}" min="10" max="72">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="font_color" class="form-label">Font Color</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="font_color" name="font_color" 
                                           value="{{ active_config.font_color }}">
                                    <span class="input-group-text colorpicker-input-addon"><i></i></span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="font_outline_color" class="form-label">Outline Color</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="font_outline_color" name="font_outline_color" 
                                           value="{{ active_config.font_outline_color }}">
                                    <span class="input-group-text colorpicker-input-addon"><i></i></span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="font_outline_width" class="form-label">Outline Width (px)</label>
                                <input type="number" class="form-control" id="font_outline_width" name="font_outline_width" 
                                       value="{{ active_config.font_outline_width }}" min="0" max="10">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="text_position" class="form-label">Text Position</label>
                                <select class="form-select" id="text_position" name="text_position">
                                    <option value="top-left" {% if active_config.text_position == 'top-left' %}selected{% endif %}>Top Left</option>
                                    <option value="top-right" {% if active_config.text_position == 'top-right' %}selected{% endif %}>Top Right</option>
                                    <option value="bottom-left" {% if active_config.text_position == 'bottom-left' %}selected{% endif %}>Bottom Left</option>
                                    <option value="bottom-right" {% if active_config.text_position == 'bottom-right' %}selected{% endif %}>Bottom Right</option>
                                    <option value="center" {% if active_config.text_position == 'center' %}selected{% endif %}>Center</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- More settings sections would go here -->
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i> SAVE CONFIGURATION
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Configuration Modal -->
<div class="modal fade" id="addConfigModal" tabindex="-1" aria-labelledby="addConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addConfigModalLabel">Add New Configuration</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('main.add_overlay_config') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_config_name" class="form-label">Configuration Name</label>
                        <input type="text" class="form-control" id="new_config_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="new_config_is_default" name="is_default">
                            <label class="form-check-label" for="new_config_is_default">
                                Set as default configuration
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> Create Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Configuration Modal -->
<div class="modal fade" id="deleteConfigModal" tabindex="-1" aria-labelledby="deleteConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteConfigModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the configuration "<span id="delete_config_name"></span>"?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('main.delete_overlay_config') }}" method="post">
                    <input type="hidden" id="delete_config_id" name="config_id">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i> Delete Configuration
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap-colorpicker@3.4.0/dist/js/bootstrap-colorpicker.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize color pickers
        $('#font_color, #font_outline_color, #marker_color, #marker_outline_color, #scoring_zone_color, #background_color').each(function() {
            $(this).colorpicker();
        });
        
        // Delete configuration modal
        $('.delete-config-btn').click(function() {
            const configId = $(this).data('config-id');
            const configName = $(this).data('config-name');
            
            $('#delete_config_id').val(configId);
            $('#delete_config_name').text(configName);
        });
        
        // Preview update
        let previewTimer;
        $('form input, form select').on('change', function() {
            // Clear previous timer
            clearTimeout(previewTimer);
            
            // Show loading indicator
            $('#previewLoader').show();
            
            // Set a new timer to update preview
            previewTimer = setTimeout(function() {
                updatePreview();
            }, 500);
        });
        
        function updatePreview() {
            // Get form data
            const formData = $('form').serialize();
            
            // Send AJAX request to update preview
            $.ajax({
                url: '{{ url_for("main.preview_overlay_config", id=active_config.id) }}',
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Update preview image with timestamp to force reload
                        $('#previewImage').attr('src', response.preview_url + '?t=' + new Date().getTime());
                    } else {
                        console.error('Preview update failed:', response.error);
                    }
                },
                error: function() {
                    console.error('Preview update request failed');
                },
                complete: function() {
                    // Hide loading indicator
                    $('#previewLoader').hide();
                }
            });
        }
    });
</script>
{% endblock %}

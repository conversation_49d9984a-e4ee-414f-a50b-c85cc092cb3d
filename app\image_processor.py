"""
Image Processing Utility for Armed Forces Shooting Range

This module provides utilities for processing target images, adding overlays,
and visualizing shot data.
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageColor
from datetime import datetime
from app.models import ImageOverlayConfig, Shot, ShootingSession, Shooter, Weapon

class ImageProcessor:
    """Utility class for processing target images and adding overlays"""

    def __init__(self, app=None):
        self.app = app
        self.default_config = None

        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        """Initialize with Flask app context"""
        self.app = app

        # Set up font paths
        self.font_path = os.path.join(app.static_folder, 'fonts')
        os.makedirs(self.font_path, exist_ok=True)

        # Default font (fallback)
        self.default_font_path = os.path.join(self.font_path, 'arial.ttf')

    def _ensure_default_config(self):
        """Ensure a default configuration exists"""
        from app import db

        # Check if a default config exists
        default_config = ImageOverlayConfig.query.filter_by(is_default=True).first()

        if not default_config:
            # Create a default configuration
            default_config = ImageOverlayConfig(
                name="Default Configuration",
                is_default=True
            )
            db.session.add(default_config)
            db.session.commit()

        self.default_config = default_config

    def get_config(self, config_id=None):
        """Get overlay configuration by ID or default"""
        if config_id:
            config = ImageOverlayConfig.query.get(config_id)
            if config:
                return config

        # Return default config or create one if needed
        if not self.default_config:
            self._ensure_default_config()

        return self.default_config

    def get_font(self, font_family, font_size):
        """Get PIL ImageFont object for the specified font family and size"""
        try:
            # Try to find the font in our font directory
            font_file = f"{font_family.lower()}.ttf"
            font_path = os.path.join(self.font_path, font_file)

            if os.path.exists(font_path):
                return ImageFont.truetype(font_path, font_size)

            # Try system fonts
            return ImageFont.truetype(font_family, font_size)
        except Exception:
            # Fallback to default font
            try:
                return ImageFont.truetype(self.default_font_path, font_size)
            except Exception:
                # Last resort: use default PIL font
                return ImageFont.load_default()

    def process_shot_image(self, shot_id, config_id=None):
        """Process a shot image with overlays based on configuration"""
        shot = Shot.query.get(shot_id)
        if not shot or not shot.image_path:
            return None

        # Get the configuration
        config = self.get_config(config_id)

        # Load the image
        image_path = os.path.join(self.app.static_folder, shot.image_path)
        if not os.path.exists(image_path):
            return None

        try:
            # Open image with PIL
            image = Image.open(image_path)

            # Create a drawing context
            draw = ImageDraw.Draw(image)

            # Add overlays based on configuration
            self._add_shot_marker(image, draw, shot, config)
            self._add_scoring_zones(image, draw, shot, config)
            self._add_information(image, draw, shot, config)
            self._add_watermark(image, draw, config)

            # Save the processed image
            processed_dir = os.path.join(os.path.dirname(image_path), 'processed')
            os.makedirs(processed_dir, exist_ok=True)

            processed_path = os.path.join(processed_dir, os.path.basename(image_path))
            image.save(processed_path)

            # Return the relative path for the processed image
            return os.path.join(os.path.dirname(shot.image_path), 'processed', os.path.basename(shot.image_path))

        except Exception as e:
            print(f"Error processing image: {e}")
            return None

    def _add_shot_marker(self, image, draw, shot, config):
        """Add shot marker to the image"""
        if not shot.x_coordinate or not shot.y_coordinate:
            return

        # Get image dimensions
        width, height = image.size

        # Convert normalized coordinates to pixel coordinates
        x = int(shot.x_coordinate * width)
        y = int(shot.y_coordinate * height)

        # Get marker settings
        marker_size = config.marker_size
        marker_color = ImageColor.getrgb(config.marker_color)
        outline_color = ImageColor.getrgb(config.marker_outline_color)
        outline_width = config.marker_outline_width
        marker_style = config.marker_style

        # Draw marker based on style
        if marker_style == 'circle':
            # Draw outline
            draw.ellipse(
                [(x - marker_size - outline_width, y - marker_size - outline_width),
                 (x + marker_size + outline_width, y + marker_size + outline_width)],
                fill=outline_color
            )
            # Draw marker
            draw.ellipse(
                [(x - marker_size, y - marker_size),
                 (x + marker_size, y + marker_size)],
                fill=marker_color
            )
        elif marker_style == 'cross':
            # Draw outline
            line_width = marker_size // 2
            draw.line(
                [(x - marker_size - outline_width, y - outline_width),
                 (x + marker_size + outline_width, y + outline_width)],
                fill=outline_color,
                width=line_width + outline_width * 2
            )
            draw.line(
                [(x - outline_width, y - marker_size - outline_width),
                 (x + outline_width, y + marker_size + outline_width)],
                fill=outline_color,
                width=line_width + outline_width * 2
            )
            # Draw marker
            draw.line(
                [(x - marker_size, y), (x + marker_size, y)],
                fill=marker_color,
                width=line_width
            )
            draw.line(
                [(x, y - marker_size), (x, y + marker_size)],
                fill=marker_color,
                width=line_width
            )
        elif marker_style == 'square':
            # Draw outline
            draw.rectangle(
                [(x - marker_size - outline_width, y - marker_size - outline_width),
                 (x + marker_size + outline_width, y + marker_size + outline_width)],
                fill=outline_color
            )
            # Draw marker
            draw.rectangle(
                [(x - marker_size, y - marker_size),
                 (x + marker_size, y + marker_size)],
                fill=marker_color
            )
        elif marker_style == 'diamond':
            # Create diamond points
            points = [
                (x, y - marker_size),  # Top
                (x + marker_size, y),  # Right
                (x, y + marker_size),  # Bottom
                (x - marker_size, y)   # Left
            ]
            outline_points = [
                (x, y - marker_size - outline_width),  # Top
                (x + marker_size + outline_width, y),  # Right
                (x, y + marker_size + outline_width),  # Bottom
                (x - marker_size - outline_width, y)   # Left
            ]
            # Draw outline
            draw.polygon(outline_points, fill=outline_color)
            # Draw marker
            draw.polygon(points, fill=marker_color)

    def _add_scoring_zones(self, image, draw, shot, config):
        """Add scoring zones to the image"""
        if not config.show_scoring_zones:
            return

        # Implementation will depend on the target type and scoring system
        # This is a placeholder for future implementation
        pass

    def _add_information(self, image, draw, shot, config):
        """Add information overlay to the image"""
        # Get image dimensions
        width, height = image.size

        # Prepare text to display
        text_lines = []

        if config.show_score and shot.score is not None:
            text_lines.append(f"Score: {shot.score:.2f}")

        if config.show_timestamp and shot.timestamp:
            text_lines.append(f"Time: {shot.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

        if config.show_coordinates and shot.x_coordinate is not None and shot.y_coordinate is not None:
            text_lines.append(f"Coords: ({shot.x_coordinate:.3f}, {shot.y_coordinate:.3f})")

        if config.show_session_info and shot.session:
            session = shot.session
            text_lines.append(f"Session: {session.date.strftime('%Y-%m-%d')}")
            text_lines.append(f"Distance: {session.distance}m")
            text_lines.append(f"Target: {session.target_type}")

        if config.show_shooter_info and shot.session and shot.session.shooter:
            shooter = shot.session.shooter
            text_lines.append(f"Shooter: {shooter.full_name}")
            if shooter.army_number:
                text_lines.append(f"Army #: {shooter.army_number}")

        if not text_lines:
            return

        # Get font
        font = self.get_font(config.font_family, config.font_size)
        font_color = ImageColor.getrgb(config.font_color)
        outline_color = ImageColor.getrgb(config.font_outline_color)

        # Calculate text block size
        line_height = config.font_size + 4
        text_height = line_height * len(text_lines)
        max_text_width = max([font.getlength(line) for line in text_lines])

        # Determine position based on text_position setting
        padding = 20
        if config.text_position == 'top-left':
            x, y = padding, padding
        elif config.text_position == 'top-right':
            x, y = width - max_text_width - padding, padding
        elif config.text_position == 'bottom-left':
            x, y = padding, height - text_height - padding
        elif config.text_position == 'bottom-right':
            x, y = width - max_text_width - padding, height - text_height - padding
        elif config.text_position == 'center':
            x, y = (width - max_text_width) // 2, (height - text_height) // 2
        else:
            x, y = padding, height - text_height - padding  # Default to bottom-left

        # Draw text with outline
        for i, line in enumerate(text_lines):
            y_pos = y + i * line_height

            # Draw text outline
            outline_width = config.font_outline_width
            for dx in range(-outline_width, outline_width + 1):
                for dy in range(-outline_width, outline_width + 1):
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y_pos + dy), line, font=font, fill=outline_color)

            # Draw text
            draw.text((x, y_pos), line, font=font, fill=font_color)

    def _add_watermark(self, image, draw, config):
        """Add watermark to the image"""
        if not config.watermark_text or config.watermark_opacity <= 0:
            return

        # Get image dimensions
        width, height = image.size

        # Get font
        font_size = int(min(width, height) * 0.05)  # Scale font size based on image size
        font = self.get_font(config.font_family, font_size)

        # Calculate watermark position (center)
        text_width = font.getlength(config.watermark_text)
        x = (width - text_width) // 2
        y = height - font_size - 20  # Near bottom

        # Create semi-transparent color
        r, g, b = ImageColor.getrgb(config.font_color)
        watermark_color = (r, g, b, int(255 * config.watermark_opacity))

        # Create a transparent overlay
        overlay = Image.new('RGBA', image.size, (0, 0, 0, 0))
        overlay_draw = ImageDraw.Draw(overlay)

        # Draw watermark on overlay
        overlay_draw.text((x, y), config.watermark_text, font=font, fill=watermark_color)

        # Composite the overlay onto the original image
        image.paste(overlay, (0, 0), overlay)

# Initialize the image processor
image_processor = ImageProcessor()

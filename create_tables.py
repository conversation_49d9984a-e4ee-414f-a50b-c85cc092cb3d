from app import create_app, db
from app.models import ImageOverlayConfig

app = create_app()

with app.app_context():
    # Create tables
    db.create_all()
    
    # Create default overlay config if it doesn't exist
    default_config = ImageOverlayConfig.query.filter_by(is_default=True).first()
    if not default_config:
        default_config = ImageOverlayConfig(
            name="Default Configuration",
            is_default=True
        )
        db.session.add(default_config)
        db.session.commit()
        print("Created default image overlay configuration")
    else:
        print("Default image overlay configuration already exists")
    
    print("Database tables created successfully")

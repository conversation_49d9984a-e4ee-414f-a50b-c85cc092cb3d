// Reports.js - JavaScript for the reports page

// Chart color scheme
const chartColors = {
    primary: '#1a3a1a',
    success: '#4b5320',
    info: '#2c3e50',
    warning: '#d4ac0d',
    danger: '#8b0000',
    light: '#f8f9fa',
    dark: '#343a40',
    gray: '#6c757d',
    primaryLight: '#8a9a5b',
    successLight: '#b2a27d',
    infoLight: '#4a6b8a',
    warningLight: '#e9c46a',
    dangerLight: '#c45850'
};

// Initialize charts
let performanceChart = null;
let scoreDistributionChart = null;
let topPerformersChart = null;
let weaponComparisonChart = null;

// Sample data for initial charts
const sampleData = {
    performanceData: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Average Score',
            data: [7.2, 7.5, 7.8, 7.6, 8.1, 8.4],
            borderColor: chartColors.primary,
            backgroundColor: 'rgba(26, 58, 26, 0.1)',
            fill: true,
            tension: 0.4
        }]
    },
    scoreDistribution: {
        labels: ['0-2', '2-4', '4-6', '6-8', '8-10'],
        data: [5, 10, 25, 40, 20]
    },
    topPerformers: {
        labels: ['<PERSON>, J.', '<PERSON>, M.', '<PERSON>, R.', '<PERSON>, D.', '<PERSON>, T.'],
        data: [9.2, 8.9, 8.7, 8.5, 8.3]
    },
    weaponComparison: {
        labels: ['Rifle', 'Pistol', 'Shotgun', 'Sniper Rifle', 'Assault Rifle'],
        data: [8.2, 7.5, 6.8, 9.1, 8.7]
    }
};

// Initialize charts with sample data
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();

    // Add event listeners
    document.getElementById('generateReportBtn').addEventListener('click', generateReport);
    document.getElementById('exportPdfBtn').addEventListener('click', exportPDF);
    document.getElementById('exportCsvBtn').addEventListener('click', exportCSV);

    // Filter change events
    document.getElementById('reportType').addEventListener('change', updateFilterVisibility);
    document.getElementById('groupSelect').addEventListener('change', function() {
        if (this.value) {
            document.getElementById('shooterSelect').value = '';
        }
    });
    document.getElementById('shooterSelect').addEventListener('change', function() {
        if (this.value) {
            document.getElementById('groupSelect').value = '';
        }
    });

    // Initialize filter visibility
    updateFilterVisibility();

    // Update dropdown button text based on current report
    const urlParams = new URLSearchParams(window.location.search);
    const reportType = urlParams.get('report_type');

    if (reportType) {
        // Find the dropdown item with this report type
        const reportLinks = document.querySelectorAll('.dropdown-menu .dropdown-item');
        for (const link of reportLinks) {
            if (link.href.includes(`report_type=${reportType}`)) {
                // Update the dropdown button text
                const icon = link.querySelector('i').cloneNode(true);
                const text = link.textContent.trim();
                const dropdownButton = document.getElementById('reportTypeDropdown');
                dropdownButton.innerHTML = '';
                dropdownButton.appendChild(icon);
                dropdownButton.appendChild(document.createTextNode(' ' + text));
                break;
            }
        }
    }
});

// Initialize charts with sample data
function initializeCharts() {
    // Performance Over Time Chart
    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
    performanceChart = new Chart(performanceCtx, {
        type: 'line',
        data: sampleData.performanceData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: {
                y: {
                    min: 0,
                    max: 10,
                    title: {
                        display: true,
                        text: 'Score'
                    }
                }
            }
        }
    });

    // Score Distribution Chart
    const scoreDistributionCtx = document.getElementById('scoreDistributionChart').getContext('2d');
    scoreDistributionChart = new Chart(scoreDistributionCtx, {
        type: 'pie',
        data: {
            labels: sampleData.scoreDistribution.labels,
            datasets: [{
                data: sampleData.scoreDistribution.data,
                backgroundColor: [
                    chartColors.danger,
                    chartColors.warning,
                    chartColors.info,
                    chartColors.success,
                    chartColors.primary
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                }
            }
        }
    });

    // Top Performers Chart
    const topPerformersCtx = document.getElementById('topPerformersChart').getContext('2d');
    topPerformersChart = new Chart(topPerformersCtx, {
        type: 'bar',
        data: {
            labels: sampleData.topPerformers.labels,
            datasets: [{
                label: 'Average Score',
                data: sampleData.topPerformers.data,
                backgroundColor: chartColors.info,
                borderColor: chartColors.infoLight,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    min: 0,
                    max: 10,
                    title: {
                        display: true,
                        text: 'Score'
                    }
                }
            }
        }
    });

    // Weapon Comparison Chart
    const weaponComparisonCtx = document.getElementById('weaponComparisonChart').getContext('2d');
    weaponComparisonChart = new Chart(weaponComparisonCtx, {
        type: 'radar',
        data: {
            labels: sampleData.weaponComparison.labels,
            datasets: [{
                label: 'Average Score',
                data: sampleData.weaponComparison.data,
                backgroundColor: 'rgba(212, 172, 13, 0.2)',
                borderColor: chartColors.warning,
                pointBackgroundColor: chartColors.warning,
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: chartColors.warning
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            elements: {
                line: {
                    borderWidth: 3
                }
            },
            scales: {
                r: {
                    angleLines: {
                        display: true
                    },
                    suggestedMin: 0,
                    suggestedMax: 10
                }
            }
        }
    });
}

// Update filter visibility based on report type
function updateFilterVisibility() {
    const reportType = document.getElementById('reportType').value;
    const shooterSelect = document.getElementById('shooterSelect').parentElement;
    const groupSelect = document.getElementById('groupSelect').parentElement;

    switch (reportType) {
        case 'individual':
            shooterSelect.style.display = 'block';
            groupSelect.style.display = 'none';
            break;
        case 'group':
            shooterSelect.style.display = 'none';
            groupSelect.style.display = 'block';
            break;
        case 'weapon':
        case 'trend':
            shooterSelect.style.display = 'block';
            groupSelect.style.display = 'block';
            break;
    }
}

// Generate report based on selected filters
function generateReport() {
    const reportType = document.getElementById('reportType').value;
    const shooterId = document.getElementById('shooterSelect').value;
    const groupId = document.getElementById('groupSelect').value;
    const dateRange = document.getElementById('dateRange').value;

    // Show loading state
    document.getElementById('generateReportBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> LOADING...';
    document.getElementById('generateReportBtn').disabled = true;

    // Fetch report data from server
    fetch(`/api/reports?type=${reportType}&shooter_id=${shooterId}&group_id=${groupId}&date_range=${dateRange}`)
        .then(response => response.json())
        .then(data => {
            updateCharts(data);
            updateStats(data.stats);
            updateTable(data.detailed_performance);
        })
        .catch(error => {
            console.error('Error fetching report data:', error);
            alert('Failed to generate report. Please try again.');
        })
        .finally(() => {
            // Restore button state
            document.getElementById('generateReportBtn').innerHTML = '<i class="fas fa-chart-line me-2"></i> GENERATE REPORT';
            document.getElementById('generateReportBtn').disabled = false;
        });
}

// Update charts with new data
function updateCharts(data) {
    // Update Performance Over Time Chart
    performanceChart.data.labels = data.performance_over_time.labels;
    performanceChart.data.datasets[0].data = data.performance_over_time.data;
    performanceChart.update();

    // Update Score Distribution Chart
    scoreDistributionChart.data.labels = data.score_distribution.labels;
    scoreDistributionChart.data.datasets[0].data = data.score_distribution.data;
    scoreDistributionChart.update();

    // Update Top Performers Chart
    topPerformersChart.data.labels = data.top_performers.labels;
    topPerformersChart.data.datasets[0].data = data.top_performers.data;
    topPerformersChart.update();

    // Update Weapon Comparison Chart
    weaponComparisonChart.data.labels = data.weapon_comparison.labels;
    weaponComparisonChart.data.datasets[0].data = data.weapon_comparison.data;
    weaponComparisonChart.update();
}

// Update statistics
function updateStats(stats) {
    document.getElementById('totalSessionsStat').textContent = stats.total_sessions;
    document.getElementById('totalShotsStat').textContent = stats.total_shots;
    document.getElementById('avgScoreStat').textContent = stats.avg_score;
    document.getElementById('accuracyStat').textContent = stats.accuracy + '%';
}

// Update performance table
function updateTable(data) {
    const tableBody = document.getElementById('performanceTableBody');
    tableBody.innerHTML = '';

    data.forEach(shooter => {
        const row = document.createElement('tr');

        // Performance indicator class
        let performanceClass = 'performance-average';
        let performanceText = 'Average';

        if (shooter.performance === 'excellent') {
            performanceClass = 'performance-excellent';
            performanceText = 'Excellent';
        } else if (shooter.performance === 'good') {
            performanceClass = 'performance-good';
            performanceText = 'Good';
        } else if (shooter.performance === 'poor') {
            performanceClass = 'performance-poor';
            performanceText = 'Needs Improvement';
        }

        row.innerHTML = `
            <td>${shooter.full_name}</td>
            <td>${shooter.army_number}</td>
            <td>${shooter.group_name || 'N/A'}</td>
            <td>${shooter.session_count}</td>
            <td>${shooter.avg_score}</td>
            <td>${shooter.best_score}</td>
            <td>${shooter.improvement}%</td>
            <td>
                <span class="performance-indicator ${performanceClass}"></span>
                ${performanceText}
            </td>
        `;

        tableBody.appendChild(row);
    });
}

// Export report as PDF
function exportPDF() {
    alert('PDF export functionality will be implemented in a future update.');
}

// Export report as CSV
function exportCSV() {
    alert('CSV export functionality will be implemented in a future update.');
}

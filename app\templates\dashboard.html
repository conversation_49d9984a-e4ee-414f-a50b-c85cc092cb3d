{% extends "base.html" %}

{% block title %}Command Center - Armed Forces Shooting Range{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="military-badge">SECURE COMMAND CENTER</div>
        <h1>TACTICAL DASHBOARD</h1>
        <p class="lead">Welcome back, OFFICER {{ current_user.username | upper }}!</p>
        <div class="military-divider"></div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">TACTICAL OPERATIONS</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('camera_bp.view_cameras') }}" class="btn btn-outline-primary d-block">
                            <i class="fas fa-video me-2"></i> SURVEILLANCE
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('main.start_session') }}" class="btn btn-outline-success d-block">
                            <i class="fas fa-flag-checkered me-2"></i> NEW MISSION
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('main.add_shooter') }}" class="btn btn-outline-info d-block">
                            <i class="fas fa-user-plus me-2"></i> ADD PERSONNEL
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('main.add_weapon') }}" class="btn btn-outline-dark d-block">
                            <i class="fas fa-crosshairs me-2"></i> ADD ARMAMENT
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">SURVEILLANCE STATUS</h5>
                <a href="{{ url_for('camera_bp.camera_settings') }}" class="btn btn-sm btn-light">
                    <i class="fas fa-cog"></i> Configure
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Camera</th>
                                <th>Status</th>
                                <th>Last Activity</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for camera_id, status in camera_status.items() %}
                            <tr>
                                <td>Camera {{ camera_id }}</td>
                                <td>
                                    {% if status.connected %}
                                    <span class="badge bg-success">Connected</span>
                                    {% else %}
                                    <span class="badge bg-danger">Disconnected</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if status.last_frame_time %}
                                    {{ status.last_frame_time.strftime('%H:%M:%S') }}
                                    {% else %}
                                    N/A
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('camera_bp.view_cameras') }}" class="btn btn-sm btn-primary">View All Cameras</a>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">RECENT MISSIONS</h5>
            </div>
            <div class="card-body">
                {% if recent_sessions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Shooter</th>
                                <th>Weapon</th>
                                <th>Shots</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in recent_sessions %}
                            <tr>
                                <td>{{ session.date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ session.shooter.full_name }}</td>
                                <td>{{ session.weapon.name }}</td>
                                <td>{{ session.shots.count() }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No recent sessions found.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('main.sessions') }}" class="btn btn-sm btn-primary">View All Sessions</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">COMBAT UNITS</h5>
            </div>
            <div class="card-body">
                {% if shooter_groups %}
                <div class="row">
                    {% for group in shooter_groups %}
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">{{ group.name }}</h5>
                                <p class="card-text">{{ group.description }}</p>
                                <p class="card-text"><small class="text-muted">Members: {{ group.shooters.count() }}</small></p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">No shooter groups found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

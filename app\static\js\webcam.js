// Webcam capture functionality for the shooting range application

let webcamStream = null;
let canvas = null;
let photoInput = null;

// Initialize webcam when the modal is shown
function initWebcam() {
    const video = document.getElementById('webcam-video');
    canvas = document.getElementById('webcam-canvas');
    photoInput = document.getElementById('photo');
    
    // Get access to the webcam
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(function(stream) {
                webcamStream = stream;
                video.srcObject = stream;
                video.play();
                
                // Enable the capture button
                document.getElementById('capture-photo').disabled = false;
            })
            .catch(function(error) {
                console.error("Error accessing webcam:", error);
                document.getElementById('webcam-error').textContent = 
                    "Could not access webcam. Please ensure your camera is connected and you've granted permission.";
                document.getElementById('webcam-error').style.display = "block";
            });
    } else {
        document.getElementById('webcam-error').textContent = 
            "Your browser does not support webcam access. Please use a modern browser like Chrome, Firefox, or Edge.";
        document.getElementById('webcam-error').style.display = "block";
    }
}

// Stop the webcam stream when the modal is closed
function stopWebcam() {
    if (webcamStream) {
        webcamStream.getTracks().forEach(track => {
            track.stop();
        });
        webcamStream = null;
        
        // Disable the capture button
        document.getElementById('capture-photo').disabled = true;
    }
}

// Capture a photo from the webcam
function capturePhoto() {
    const video = document.getElementById('webcam-video');
    const context = canvas.getContext('2d');
    
    // Draw the video frame to the canvas
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // Show the captured photo
    document.getElementById('captured-photo').src = canvas.toDataURL('image/png');
    document.getElementById('captured-photo').style.display = 'block';
    document.getElementById('capture-controls').style.display = 'none';
    document.getElementById('review-controls').style.display = 'flex';
}

// Retake the photo
function retakePhoto() {
    document.getElementById('captured-photo').style.display = 'none';
    document.getElementById('capture-controls').style.display = 'flex';
    document.getElementById('review-controls').style.display = 'none';
}

// Use the captured photo
function usePhoto() {
    // Convert the canvas image to a Blob
    canvas.toBlob(function(blob) {
        // Create a File object from the Blob
        const file = new File([blob], "webcam-photo.png", { type: "image/png" });
        
        // Create a FileList-like object
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        
        // Set the file to the file input
        photoInput.files = dataTransfer.files;
        
        // Show the preview
        const previewImg = document.getElementById('photo-preview');
        if (previewImg) {
            previewImg.src = URL.createObjectURL(file);
            previewImg.style.display = 'block';
        }
        
        // Close the modal
        const webcamModal = bootstrap.Modal.getInstance(document.getElementById('webcamModal'));
        webcamModal.hide();
    }, 'image/png');
}

// Initialize event listeners when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the webcam when the modal is shown
    document.getElementById('webcamModal').addEventListener('shown.bs.modal', initWebcam);
    
    // Stop the webcam when the modal is hidden
    document.getElementById('webcamModal').addEventListener('hidden.bs.modal', stopWebcam);
    
    // Capture photo button
    document.getElementById('capture-photo').addEventListener('click', capturePhoto);
    
    // Retake photo button
    document.getElementById('retake-photo').addEventListener('click', retakePhoto);
    
    // Use photo button
    document.getElementById('use-photo').addEventListener('click', usePhoto);
});

import sqlite3
import os

# Path to the database file
db_path = 'instance/app.db'

# Check if the database file exists
if not os.path.exists(db_path):
    print(f"Database file {db_path} not found.")
    exit(1)

# Connect to the database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Check if the photo_path column already exists in the weapon table
cursor.execute("PRAGMA table_info(weapon)")
columns = cursor.fetchall()
column_names = [column[1] for column in columns]

if 'photo_path' not in column_names:
    print("Adding photo_path column to weapon table...")
    cursor.execute("ALTER TABLE weapon ADD COLUMN photo_path TEXT")
    conn.commit()
    print("Column added successfully.")
else:
    print("photo_path column already exists in weapon table.")

# Check if the photo_path column already exists in the shooter table
cursor.execute("PRAGMA table_info(shooter)")
columns = cursor.fetchall()
column_names = [column[1] for column in columns]

if 'photo_path' not in column_names:
    print("Adding photo_path column to shooter table...")
    cursor.execute("ALTER TABLE shooter ADD COLUMN photo_path TEXT")
    conn.commit()
    print("Column added successfully.")
else:
    print("photo_path column already exists in shooter table.")

# Close the connection
conn.close()
print("Database update completed.")

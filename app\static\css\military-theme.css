/* Military Theme for Shooting Range Application */

:root {
    --military-dark-green: #1a3a1a;
    --military-green: #4b5320;
    --military-light-green: #8a9a5b;
    --military-tan: #b2a27d;
    --military-brown: #7f6a49;
    --military-black: #1e1e1e;
    --military-red: #8b0000;
    --military-blue: #2c3e50;
    --military-yellow: #d4ac0d;
}

/* Body and background */
body {
    background-color: #f5f5f5;
    font-family: 'Roboto', sans-serif;
    position: relative;
}

body::before {
    content: "";
    background-image: url('../images/theme/military-background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.35;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

/* Navbar */
.navbar {
    background-color: var(--military-dark-green) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-bottom: 3px solid var(--military-yellow);
}

.navbar-brand {
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--military-yellow);
}

/* Cards */
.card {
    border: none;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: rgba(255, 255, 255, 0.95);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 2px solid var(--military-yellow);
}

.card-header.bg-primary {
    background-color: var(--military-dark-green) !important;
}

.card-header.bg-success {
    background-color: var(--military-green) !important;
}

.card-header.bg-info {
    background-color: var(--military-blue) !important;
}

.card-header.bg-warning {
    background-color: var(--military-tan) !important;
    color: var(--military-black) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--military-dark-green);
    border-color: var(--military-dark-green);
}

.btn-primary:hover {
    background-color: #132b13;
    border-color: #132b13;
}

.btn-success {
    background-color: var(--military-green);
    border-color: var(--military-green);
}

.btn-success:hover {
    background-color: #3a4219;
    border-color: #3a4219;
}

.btn-info {
    background-color: var(--military-blue);
    border-color: var(--military-blue);
    color: white;
}

.btn-info:hover {
    background-color: #1e2b3c;
    border-color: #1e2b3c;
    color: white;
}

.btn-outline-primary {
    color: var(--military-dark-green);
    border-color: var(--military-dark-green);
}

.btn-outline-primary:hover {
    background-color: var(--military-dark-green);
    border-color: var(--military-dark-green);
}

/* Tables */
.table thead th {
    background-color: var(--military-tan);
    color: var(--military-black);
    border-bottom: 2px solid var(--military-yellow);
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.table-hover tbody tr:hover {
    background-color: rgba(139, 146, 112, 0.1);
}

/* Footer */
.footer {
    background-color: var(--military-dark-green) !important;
    color: white;
    border-top: 3px solid var(--military-yellow);
}

.footer .text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Badges */
.badge.bg-success {
    background-color: var(--military-green) !important;
}

.badge.bg-danger {
    background-color: var(--military-red) !important;
}

.badge.bg-info {
    background-color: var(--military-blue) !important;
}

.badge.bg-warning {
    background-color: var(--military-yellow) !important;
    color: var(--military-black);
}

/* Alerts */
.alert-info {
    background-color: rgba(44, 62, 80, 0.1);
    border-color: var(--military-blue);
    color: var(--military-blue);
}

/* Forms */
.form-control:focus {
    border-color: var(--military-green);
    box-shadow: 0 0 0 0.25rem rgba(75, 83, 32, 0.25);
}

/* Military decorative elements */
.military-divider {
    height: 3px;
    background: linear-gradient(to right, transparent, var(--military-yellow), transparent);
    margin: 2rem 0;
}

.military-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: var(--military-dark-green);
    color: white;
    border-radius: 3px;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    border-left: 4px solid var(--military-yellow);
}

/* Custom profile image styling */
.profile-image, .profile-image-small {
    border: 3px solid var(--military-tan);
}

/* Form styling for group/individual selection */
#group-selection {
    display: none;
}

/* Camera view styling */
#cameraView {
    display: none;
}

/* Camera assignment styling */
.camera-assignment {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 0.5rem;
}

.camera-assignment-form .input-group {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.camera-assignment-form .form-select {
    border-color: var(--military-tan);
}

.camera-assignment-form .btn {
    border-color: var(--military-tan);
    color: var(--military-dark-green);
}

.camera-assignment-form .btn:hover {
    background-color: var(--military-tan);
    color: var(--military-black);
}

/* Page title styling */
h1 {
    color: var(--military-dark-green);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 3px solid var(--military-yellow);
    padding-bottom: 0.5rem;
    display: inline-block;
}

/* Lead text styling */
.lead {
    color: var(--military-green);
    font-weight: 500;
}

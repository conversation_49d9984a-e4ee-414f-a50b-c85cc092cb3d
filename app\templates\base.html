<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Shooting Range Camera System{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap5.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/datatables-custom.css') }}">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/webcam.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/camera-view.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/military-theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Top Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark" id="topNavbar">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-shield-alt"></i> ARMED FORCES SHOOTING RANGE
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i> Home
                        </a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('camera_bp.view_cameras') }}">
                            <i class="fas fa-video me-1"></i> Cameras
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.shooters') }}">
                            <i class="fas fa-users me-1"></i> Shooters
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.groups') }}">
                            <i class="fas fa-layer-group me-1"></i> Groups
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.weapons') }}">
                            <i class="fas fa-crosshairs me-1"></i> Weapons
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.sessions') }}">
                            <i class="fas fa-calendar-alt me-1"></i> Sessions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.reports') }}">
                            <i class="fas fa-chart-bar me-1"></i> Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.image_overlays') }}">
                            <i class="fas fa-image me-1"></i> Image Overlays
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user-circle me-2"></i> Profile
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('camera_bp.camera_settings') }}">
                                <i class="fas fa-cog me-2"></i> Camera Settings
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">Register</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar" style="display: none;">
        <div class="sidebar-header">
            <a href="{{ url_for('main.index') }}" class="logo">
                <i class="fas fa-shield-alt"></i>
                <span class="logo-text">SHOOTING RANGE</span>
            </a>
            <button type="button" class="sidebar-toggle" id="sidebarToggle" title="Toggle Sidebar">
                <i class="fas fa-angle-left"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="{{ url_for('main.index') }}" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Home</span>
                </a>
            </li>

            {% if current_user.is_authenticated %}
            <li>
                <a href="{{ url_for('main.dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="nav-text">Dashboard</span>
                </a>
            </li>

            <li>
                <a href="{{ url_for('camera_bp.view_cameras') }}" class="nav-link">
                    <i class="fas fa-video"></i>
                    <span class="nav-text">Cameras</span>
                </a>
            </li>

            <li>
                <a href="{{ url_for('main.shooters') }}" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Shooters</span>
                </a>
            </li>

            <li>
                <a href="{{ url_for('main.groups') }}" class="nav-link">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Groups</span>
                </a>
            </li>

            <li>
                <a href="{{ url_for('main.weapons') }}" class="nav-link">
                    <i class="fas fa-crosshairs"></i>
                    <span class="nav-text">Weapons</span>
                </a>
            </li>

            <li>
                <a href="{{ url_for('main.sessions') }}" class="nav-link">
                    <i class="fas fa-calendar-alt"></i>
                    <span class="nav-text">Sessions</span>
                </a>
            </li>

            <li>
                <a href="{{ url_for('main.reports') }}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span class="nav-text">Reports</span>
                </a>
            </li>

            <li>
                <a href="{{ url_for('main.image_overlays') }}" class="nav-link">
                    <i class="fas fa-image"></i>
                    <span class="nav-text">Image Overlays</span>
                </a>
            </li>

            <div class="sidebar-divider"></div>

            <li>
                <a href="{{ url_for('camera_bp.camera_settings') }}" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span class="nav-text">Camera Settings</span>
                </a>
            </li>
            {% endif %}
        </ul>

        <div class="sidebar-footer">
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('auth.profile') }}" class="nav-link">
                <i class="fas fa-user-circle"></i>
                <span class="nav-text">{{ current_user.username }}</span>
            </a>
            {% else %}
            <a href="{{ url_for('auth.login') }}" class="nav-link">
                <i class="fas fa-sign-in-alt"></i>
                <span class="nav-text">Login</span>
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Sidebar Backdrop (for mobile) -->
    <div class="sidebar-backdrop" id="sidebarBackdrop"></div>

    <!-- Layout Toggle Button -->
    <button type="button" class="layout-toggle" id="layoutToggle" title="Switch Layout">
        <i class="fas fa-columns"></i>
    </button>

    <!-- Main Content -->
    <div class="container mt-4 main-content">
        {% with messages = get_flashed_messages() %}
        {% if messages %}
        <div class="row">
            <div class="col-md-12">
                {% for message in messages %}
                <div class="alert alert-info alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-5 py-3">
        <div class="container text-center">
            <span class="text-muted">ARMED FORCES SHOOTING RANGE SYSTEM &copy; {{ now.year }}</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.1/js/responsive.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/layout-switcher.js') }}"></script>

    <!-- Initialize DataTables -->
    <script>
        $(document).ready(function() {
            // Initialize all tables with the 'datatable' class
            $('.datatable').DataTable({
                responsive: true,
                language: {
                    search: "<i class='fas fa-search'></i> _INPUT_",
                    searchPlaceholder: "Search records...",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)",
                    zeroRecords: "No matching records found",
                    paginate: {
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>"
                    }
                }
            });
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>

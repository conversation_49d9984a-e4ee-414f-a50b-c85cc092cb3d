"""Add last_login and created_at to User model

Revision ID: ea77cbec7ba7
Revises: afd29d25fe69
Create Date: 2025-05-17 17:44:14.700400

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ea77cbec7ba7'
down_revision = 'afd29d25fe69'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('created_at', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('last_login', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_column('last_login')
        batch_op.drop_column('created_at')

    # ### end Alembic commands ###

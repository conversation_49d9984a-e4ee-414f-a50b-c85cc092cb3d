import cv2
import numpy as np
import os
from datetime import datetime
from flask import current_app

class TargetAnalyzer:
    def __init__(self):
        # Default target parameters (can be customized per target type)
        self.target_center = None  # Will be set during calibration
        self.target_radius = None  # Will be set during calibration
        self.ring_values = {
            10: 0.5,    # 10-ring radius as fraction of target radius
            9: 1.0,     # 9-ring radius as fraction of target radius
            8: 1.5,     # 8-ring radius as fraction of target radius
            7: 2.0,     # 7-ring radius as fraction of target radius
            6: 2.5,     # 6-ring radius as fraction of target radius
            5: 3.0,     # 5-ring radius as fraction of target radius
            4: 3.5,     # 4-ring radius as fraction of target radius
            3: 4.0,     # 3-ring radius as fraction of target radius
            2: 4.5,     # 2-ring radius as fraction of target radius
            1: 5.0      # 1-ring radius as fraction of target radius
        }
    
    def calibrate(self, image, target_type):
        """
        Calibrate the analyzer for a specific target type and image
        
        Args:
            image: The target image (numpy array)
            target_type: String identifier for the target type
            
        Returns:
            bool: True if calibration was successful
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Use Hough Circle Transform to detect circles
            circles = cv2.HoughCircles(
                blurred,
                cv2.HOUGH_GRADIENT,
                dp=1,
                minDist=50,
                param1=50,
                param2=30,
                minRadius=20,
                maxRadius=300
            )
            
            if circles is not None:
                # Convert to integer coordinates
                circles = np.round(circles[0, :]).astype("int")
                
                # Find the largest circle (assuming it's the target)
                largest_circle = max(circles, key=lambda x: x[2])
                
                # Set target center and radius
                self.target_center = (largest_circle[0], largest_circle[1])
                self.target_radius = largest_circle[2]
                
                return True
            
            return False
        
        except Exception as e:
            print(f"Calibration error: {str(e)}")
            return False
    
    def detect_shots(self, before_image, after_image):
        """
        Detect shots by comparing before and after images
        
        Args:
            before_image: Image before the shot
            after_image: Image after the shot
            
        Returns:
            list: List of detected shot coordinates [(x, y), ...]
        """
        try:
            # Convert to grayscale
            before_gray = cv2.cvtColor(before_image, cv2.COLOR_BGR2GRAY)
            after_gray = cv2.cvtColor(after_image, cv2.COLOR_BGR2GRAY)
            
            # Calculate absolute difference
            diff = cv2.absdiff(before_gray, after_gray)
            
            # Apply threshold to get binary image
            _, thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
            
            # Apply morphological operations to remove noise
            kernel = np.ones((5, 5), np.uint8)
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours by size
            min_area = 20  # Minimum area to be considered a shot
            max_area = 500  # Maximum area to be considered a shot
            
            shot_contours = [c for c in contours if min_area < cv2.contourArea(c) < max_area]
            
            # Get shot coordinates (center of contours)
            shots = []
            for contour in shot_contours:
                M = cv2.moments(contour)
                if M["m00"] > 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    shots.append((cx, cy))
            
            return shots
        
        except Exception as e:
            print(f"Shot detection error: {str(e)}")
            return []
    
    def calculate_score(self, shot_x, shot_y):
        """
        Calculate the score for a shot based on its distance from the target center
        
        Args:
            shot_x: X coordinate of the shot
            shot_y: Y coordinate of the shot
            
        Returns:
            float: Score value
        """
        if self.target_center is None or self.target_radius is None:
            return 0.0
        
        # Calculate distance from target center
        center_x, center_y = self.target_center
        distance = np.sqrt((shot_x - center_x)**2 + (shot_y - center_y)**2)
        
        # Normalize distance as a fraction of target radius
        normalized_distance = distance / self.target_radius
        
        # Determine score based on ring values
        for score, radius in sorted(self.ring_values.items(), reverse=True):
            if normalized_distance <= radius:
                return score
        
        # Outside all rings
        return 0.0
    
    def annotate_image(self, image, shots, scores=None):
        """
        Annotate the image with shot markers and scores
        
        Args:
            image: The image to annotate
            shots: List of shot coordinates [(x, y), ...]
            scores: List of scores for each shot (optional)
            
        Returns:
            numpy.ndarray: Annotated image
        """
        # Create a copy of the image
        annotated = image.copy()
        
        # Draw target center and rings if calibrated
        if self.target_center is not None and self.target_radius is not None:
            center_x, center_y = self.target_center
            
            # Draw center point
            cv2.circle(annotated, (center_x, center_y), 3, (0, 255, 0), -1)
            
            # Draw rings
            for score, radius_factor in self.ring_values.items():
                radius = int(self.target_radius * radius_factor)
                cv2.circle(annotated, (center_x, center_y), radius, (0, 255, 0), 2)
        
        # Draw shots
        for i, (x, y) in enumerate(shots):
            # Draw shot marker
            cv2.circle(annotated, (x, y), 5, (0, 0, 255), -1)
            
            # Draw score if provided
            if scores and i < len(scores):
                score_text = f"{scores[i]:.1f}"
                cv2.putText(annotated, score_text, (x + 10, y), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        return annotated

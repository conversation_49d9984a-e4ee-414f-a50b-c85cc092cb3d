{% extends "base.html" %}

{% block title %}Shooter Groups - Armed Forces Shooting Range{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="military-badge">PERSONNEL MANAGEMENT</div>
        <h1>SHOOTER GROUPS</h1>
        <p class="lead">Manage military units and shooter groups</p>
        <div class="military-divider"></div>
    </div>
    <div class="col-md-4 text-end">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGroupModal">
            <i class="fas fa-plus me-2"></i> ADD NEW GROUP
        </button>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        {% endif %}
        {% endwith %}
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">SHOOTER GROUPS</h5>
            </div>
            <div class="card-body">
                {% if groups %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover datatable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Members</th>
                                <th style="width: 120px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for group in groups %}
                            <tr>
                                <td>{{ group.id }}</td>
                                <td>{{ group.name }}</td>
                                <td>{{ group.description }}</td>
                                <td>
                                    {% set member_count = group.shooters.count() %}
                                    <span class="badge bg-secondary">{{ member_count }} member{{ 's' if member_count != 1 else '' }}</span>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-primary view-group-btn"
                                                data-bs-toggle="modal" data-bs-target="#viewGroupModal"
                                                data-group-id="{{ group.id }}"
                                                data-group-name="{{ group.name }}"
                                                data-group-description="{{ group.description }}"
                                                title="View Group">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary edit-group-btn"
                                                data-bs-toggle="modal" data-bs-target="#editGroupModal"
                                                data-group-id="{{ group.id }}"
                                                data-group-name="{{ group.name }}"
                                                data-group-description="{{ group.description }}"
                                                title="Edit Group">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-group-btn"
                                                data-bs-toggle="modal" data-bs-target="#deleteGroupModal"
                                                data-group-id="{{ group.id }}"
                                                data-group-name="{{ group.name }}"
                                                title="Delete Group">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No shooter groups found. Click the "Add New Group" button to create one.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Group Modal -->
<div class="modal fade" id="addGroupModal" tabindex="-1" aria-labelledby="addGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addGroupModalLabel">Add New Group</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('main.add_group') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="form-text">Enter the unit or group name (e.g., "Alpha Squad", "Sniper Team")</div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        <div class="form-text">Optional description of the group's purpose or composition</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> Add Group
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Group Modal -->
<div class="modal fade" id="editGroupModal" tabindex="-1" aria-labelledby="editGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="editGroupModalLabel">Edit Group</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('main.edit_group') }}" method="post">
                <div class="modal-body">
                    <input type="hidden" id="edit_group_id" name="group_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Group Modal -->
<div class="modal fade" id="viewGroupModal" tabindex="-1" aria-labelledby="viewGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="viewGroupModalLabel">Group Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <h4 id="view_group_name"></h4>
                        <p id="view_group_description" class="text-muted"></p>
                        <div class="military-divider"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <h5>Group Members</h5>
                        <div id="group_members_container">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading group members...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Group Modal -->
<div class="modal fade" id="deleteGroupModal" tabindex="-1" aria-labelledby="deleteGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteGroupModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the group "<span id="delete_group_name"></span>"?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> This action cannot be undone. All shooters in this group will be unassigned from it.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('main.delete_group') }}" method="post">
                    <input type="hidden" id="delete_group_id" name="group_id">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i> Delete Group
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // View Group
        $('.view-group-btn').click(function() {
            const groupId = $(this).data('group-id');
            const groupName = $(this).data('group-name');
            const groupDescription = $(this).data('group-description');

            $('#view_group_name').text(groupName);
            $('#view_group_description').text(groupDescription || 'No description provided.');

            // Load group members
            $.ajax({
                url: '/group/' + groupId + '/members',
                type: 'GET',
                success: function(data) {
                    if (data.members.length > 0) {
                        let html = '<div class="list-group">';
                        data.members.forEach(function(member) {
                            html += `
                                <a href="/shooter/${member.id}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${member.full_name}</h6>
                                        <small>${member.army_number}</small>
                                    </div>
                                    <small class="text-muted">${member.experience_level}</small>
                                </a>
                            `;
                        });
                        html += '</div>';
                        $('#group_members_container').html(html);
                    } else {
                        $('#group_members_container').html('<div class="alert alert-info">No members in this group.</div>');
                    }
                },
                error: function() {
                    $('#group_members_container').html('<div class="alert alert-danger">Error loading group members.</div>');
                }
            });
        });

        // Edit Group
        $('.edit-group-btn').click(function() {
            const groupId = $(this).data('group-id');
            const groupName = $(this).data('group-name');
            const groupDescription = $(this).data('group-description');

            $('#edit_group_id').val(groupId);
            $('#edit_name').val(groupName);
            $('#edit_description').val(groupDescription);
        });

        // Delete Group
        $('.delete-group-btn').click(function() {
            const groupId = $(this).data('group-id');
            const groupName = $(this).data('group-name');

            $('#delete_group_id').val(groupId);
            $('#delete_group_name').text(groupName);
        });
    });
</script>
{% endblock %}

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, Response, current_app
from flask_login import login_user, logout_user, current_user, login_required
from werkzeug.urls import url_parse
from werkzeug.utils import secure_filename
from sqlalchemy import func, desc, and_
from app import db
from app.models import User, Shooter, ShooterGroup, Weapon, ShootingSession, Shot, ImageOverlayConfig
from app.camera import CameraManager, Camera
from app.analysis import TargetAnalyzer
from datetime import datetime, timedelta
import os
import json
import uuid
import random

# Initialize camera manager
camera_manager = CameraManager()
target_analyzer = TargetAnalyzer()

# Import image processor
from app.image_processor import ImageProcessor
image_processor = ImageProcessor()

# Create blueprints
main_bp = Blueprint('main', __name__)
auth_bp = Blueprint('auth', __name__)
camera_bp = Blueprint('camera_bp', __name__)

# Initialize camera manager and image processor with app context
@main_bp.record_once
def on_load(state):
    camera_manager.init_app(state.app)
    image_processor.init_app(state.app)

# Authentication routes
@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        remember_me = 'remember_me' in request.form

        user = User.query.filter_by(username=username).first()

        if user is None or not user.check_password(password):
            flash('Invalid username or password')
            return redirect(url_for('auth.login'))

        # Update last login time
        user.last_login = datetime.utcnow()
        db.session.commit()

        login_user(user, remember=remember_me)
        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('main.index')

        return redirect(next_page)

    return render_template('login.html', title='Sign In')

@auth_bp.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('main.index'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']

        user = User.query.filter_by(username=username).first()
        if user is not None:
            flash('Username already taken')
            return redirect(url_for('auth.register'))

        user = User.query.filter_by(email=email).first()
        if user is not None:
            flash('Email already registered')
            return redirect(url_for('auth.register'))

        user = User(username=username, email=email)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()

        flash('Registration successful! You can now log in.')
        return redirect(url_for('auth.login'))

    return render_template('register.html', title='Register')

@auth_bp.route('/profile')
@login_required
def profile():
    """User profile page"""
    return render_template('profile.html', title='User Profile')

@auth_bp.route('/profile/update', methods=['POST'])
@login_required
def update_profile():
    """Update user profile information"""
    username = request.form['username']
    email = request.form['email']

    # Check if username is already taken by another user
    user = User.query.filter(User.username == username, User.id != current_user.id).first()
    if user:
        flash('Username already taken', 'danger')
        return redirect(url_for('auth.profile'))

    # Check if email is already registered by another user
    user = User.query.filter(User.email == email, User.id != current_user.id).first()
    if user:
        flash('Email already registered', 'danger')
        return redirect(url_for('auth.profile'))

    # Update user information
    current_user.username = username
    current_user.email = email
    db.session.commit()

    flash('Profile updated successfully!', 'success')
    return redirect(url_for('auth.profile'))

@auth_bp.route('/profile/change-password', methods=['POST'])
@login_required
def change_password():
    """Change user password"""
    current_password = request.form['current_password']
    new_password = request.form['new_password']
    confirm_password = request.form['confirm_password']

    # Check if current password is correct
    if not current_user.check_password(current_password):
        flash('Current password is incorrect', 'danger')
        return redirect(url_for('auth.profile'))

    # Check if new password and confirmation match
    if new_password != confirm_password:
        flash('New password and confirmation do not match', 'danger')
        return redirect(url_for('auth.profile'))

    # Check password strength
    if len(new_password) < 8:
        flash('Password must be at least 8 characters long', 'danger')
        return redirect(url_for('auth.profile'))

    # Check if password contains letters and numbers
    if not (any(c.isalpha() for c in new_password) and any(c.isdigit() for c in new_password)):
        flash('Password must include both letters and numbers', 'danger')
        return redirect(url_for('auth.profile'))

    # Update password
    current_user.set_password(new_password)
    db.session.commit()

    flash('Password changed successfully!', 'success')
    return redirect(url_for('auth.profile'))

# Main routes
@main_bp.route('/')
@main_bp.route('/index')
def index():
    return render_template('index.html', title='Home')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    # Get camera status
    camera_status = camera_manager.get_camera_status()

    # Get recent sessions
    recent_sessions = ShootingSession.query.order_by(ShootingSession.date.desc()).limit(5).all()

    # Get shooter groups
    shooter_groups = ShooterGroup.query.all()

    return render_template('dashboard.html',
                          title='Dashboard',
                          camera_status=camera_status,
                          recent_sessions=recent_sessions,
                          shooter_groups=shooter_groups)

# Shooter routes
@main_bp.route('/shooters')
@login_required
def shooters():
    shooters = Shooter.query.all()
    groups = ShooterGroup.query.all()
    return render_template('shooters.html', title='Shooters', shooters=shooters, groups=groups)

@main_bp.route('/shooter/<int:id>')
@login_required
def shooter(id):
    shooter = Shooter.query.get_or_404(id)
    sessions = ShootingSession.query.filter_by(shooter_id=id).order_by(ShootingSession.date.desc()).all()
    return render_template('shooter_detail.html', title=shooter.full_name, shooter=shooter, sessions=sessions)

@main_bp.route('/shooter/<int:shooter_id>/assign-camera', methods=['POST'])
@login_required
def assign_camera(shooter_id):
    shooter = Shooter.query.get_or_404(shooter_id)

    # Get the camera_id from the form
    camera_id = request.form.get('camera_id', '')

    # Convert to integer or None
    if camera_id == '':
        shooter.camera_id = None
        flash(f'Camera unassigned from {shooter.full_name}.')
    else:
        # Check if this camera is already assigned to another shooter
        existing_assignment = Shooter.query.filter(Shooter.camera_id == int(camera_id), Shooter.id != shooter_id).first()
        if existing_assignment:
            flash(f'Camera {camera_id} is already assigned to {existing_assignment.full_name}. Reassigning to {shooter.full_name}.', 'warning')
            existing_assignment.camera_id = None

        shooter.camera_id = int(camera_id)
        flash(f'Camera {camera_id} assigned to {shooter.full_name}.')

    db.session.commit()
    return redirect(url_for('main.shooter', id=shooter_id))

@main_bp.route('/shooter/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_shooter(id):
    shooter = Shooter.query.get_or_404(id)
    groups = ShooterGroup.query.all()

    if request.method == 'POST':
        shooter.first_name = request.form['first_name']
        shooter.last_name = request.form['last_name']

        # Check if army number has changed
        new_army_number = request.form['army_number']
        if new_army_number != shooter.army_number:
            # Check if the new army number already exists
            existing_shooter = Shooter.query.filter(Shooter.army_number == new_army_number, Shooter.id != shooter.id).first()
            if existing_shooter:
                flash(f'Army number {new_army_number} is already registered.', 'danger')
                return render_template('edit_shooter.html', title=f'Edit {shooter.full_name}', shooter=shooter, groups=groups)

            shooter.army_number = new_army_number

        shooter.email = request.form['email']
        shooter.phone = request.form['phone']
        shooter.experience_level = request.form['experience_level']
        shooter.group_id = request.form['group_id'] if request.form['group_id'] else None

        # Check if a new photo was uploaded
        if 'photo' in request.files and request.files['photo'].filename != '':
            photo_file = request.files['photo']

            # Secure the filename and generate a unique name
            filename = secure_filename(photo_file.filename)
            unique_filename = f"{uuid.uuid4().hex}_{filename}"

            # Save the file
            photo_dir = os.path.join(current_app.static_folder, 'images/shooters')
            os.makedirs(photo_dir, exist_ok=True)
            file_path = os.path.join(photo_dir, unique_filename)
            photo_file.save(file_path)

            # Store the relative path in the database
            shooter.photo_path = f"images/shooters/{unique_filename}"

        db.session.commit()

        flash(f'Soldier {shooter.full_name} updated successfully!', 'success')
        return redirect(url_for('main.shooter', id=shooter.id))

    return render_template('edit_shooter.html', title=f'Edit {shooter.full_name}', shooter=shooter, groups=groups)

@main_bp.route('/shooter/add', methods=['GET', 'POST'])
@login_required
def add_shooter():
    if request.method == 'POST':
        first_name = request.form['first_name']
        last_name = request.form['last_name']
        army_number = request.form['army_number']
        email = request.form['email']
        phone = request.form['phone']
        experience_level = request.form['experience_level']
        group_id = request.form['group_id']

        # Check if army number already exists
        existing_shooter = Shooter.query.filter_by(army_number=army_number).first()
        if existing_shooter:
            flash(f'Army number {army_number} is already registered.', 'danger')
            groups = ShooterGroup.query.all()
            return render_template('add_shooter.html', title='Add Shooter', groups=groups)

        # Initialize photo_path as None
        photo_path = None

        # Check if a photo was uploaded
        if 'photo' in request.files and request.files['photo'].filename != '':
            photo_file = request.files['photo']

            # Secure the filename and generate a unique name
            filename = secure_filename(photo_file.filename)
            unique_filename = f"{uuid.uuid4().hex}_{filename}"

            # Save the file
            photo_dir = os.path.join(current_app.static_folder, 'images/shooters')
            os.makedirs(photo_dir, exist_ok=True)
            file_path = os.path.join(photo_dir, unique_filename)
            photo_file.save(file_path)

            # Store the relative path in the database
            photo_path = f"images/shooters/{unique_filename}"

        shooter = Shooter(
            first_name=first_name,
            last_name=last_name,
            army_number=army_number,
            email=email,
            phone=phone,
            experience_level=experience_level,
            group_id=group_id,
            photo_path=photo_path,
            coach_id=current_user.id
        )

        db.session.add(shooter)
        db.session.commit()

        flash(f'Soldier {first_name} {last_name} (Army #: {army_number}) added successfully!', 'success')
        return redirect(url_for('main.shooters'))

    groups = ShooterGroup.query.all()
    return render_template('add_shooter.html', title='Add Shooter', groups=groups)

# Group routes
@main_bp.route('/groups')
@login_required
def groups():
    """Shooter groups page"""
    groups = ShooterGroup.query.all()
    return render_template('groups.html', title='Shooter Groups', groups=groups)

@main_bp.route('/group/<int:id>')
@login_required
def group(id):
    """Group detail page"""
    group = ShooterGroup.query.get_or_404(id)
    shooters = Shooter.query.filter_by(group_id=id).all()
    return render_template('group_detail.html', title=group.name, group=group, shooters=shooters)

@main_bp.route('/group/<int:id>/members')
@login_required
def group_members(id):
    """Get group members as JSON"""
    group = ShooterGroup.query.get_or_404(id)
    shooters = Shooter.query.filter_by(group_id=id).all()

    members = []
    for shooter in shooters:
        members.append({
            'id': shooter.id,
            'full_name': shooter.full_name,
            'army_number': shooter.army_number,
            'experience_level': shooter.experience_level
        })

    return jsonify({'members': members})

@main_bp.route('/group/add', methods=['POST'])
@login_required
def add_group():
    """Add a new shooter group"""
    name = request.form['name']
    description = request.form['description']

    # Check if group with this name already exists
    existing_group = ShooterGroup.query.filter_by(name=name).first()
    if existing_group:
        flash(f'Group with name "{name}" already exists.', 'danger')
        return redirect(url_for('main.groups'))

    group = ShooterGroup(
        name=name,
        description=description
    )

    db.session.add(group)
    db.session.commit()

    flash(f'Group {name} added successfully!', 'success')
    return redirect(url_for('main.groups'))

@main_bp.route('/group/edit', methods=['POST'])
@login_required
def edit_group():
    """Edit an existing shooter group"""
    group_id = request.form['group_id']
    name = request.form['name']
    description = request.form['description']

    group = ShooterGroup.query.get_or_404(group_id)

    # Check if another group with this name already exists
    existing_group = ShooterGroup.query.filter(ShooterGroup.name == name, ShooterGroup.id != group_id).first()
    if existing_group:
        flash(f'Group with name "{name}" already exists.', 'danger')
        return redirect(url_for('main.groups'))

    # Update group
    group.name = name
    group.description = description
    db.session.commit()

    flash(f'Group {name} updated successfully!', 'success')
    return redirect(url_for('main.groups'))

@main_bp.route('/group/delete', methods=['POST'])
@login_required
def delete_group():
    """Delete a shooter group"""
    group_id = request.form['group_id']
    group = ShooterGroup.query.get_or_404(group_id)

    # Update all shooters in this group to have no group
    shooters = Shooter.query.filter_by(group_id=group_id).all()
    for shooter in shooters:
        shooter.group_id = None

    # Delete the group
    db.session.delete(group)
    db.session.commit()

    flash(f'Group {group.name} deleted successfully!', 'success')
    return redirect(url_for('main.groups'))

# Weapon routes
@main_bp.route('/weapons')
@login_required
def weapons():
    weapons = Weapon.query.all()
    return render_template('weapons.html', title='Weapons', weapons=weapons)

@main_bp.route('/weapon/<int:id>')
@login_required
def weapon(id):
    weapon = Weapon.query.get_or_404(id)
    sessions = ShootingSession.query.filter_by(weapon_id=id).order_by(ShootingSession.date.desc()).all()
    return render_template('weapon_detail.html', title=weapon.name, weapon=weapon, sessions=sessions)

@main_bp.route('/weapon/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_weapon(id):
    weapon = Weapon.query.get_or_404(id)

    if request.method == 'POST':
        weapon.name = request.form['name']
        weapon.type = request.form['type']
        weapon.caliber = request.form['caliber']
        weapon.description = request.form['description']

        # Check if a new photo was uploaded
        if 'photo' in request.files and request.files['photo'].filename != '':
            photo_file = request.files['photo']

            # Secure the filename and generate a unique name
            filename = secure_filename(photo_file.filename)
            unique_filename = f"{uuid.uuid4().hex}_{filename}"

            # Save the file
            photo_dir = os.path.join(current_app.static_folder, 'images/weapons')
            os.makedirs(photo_dir, exist_ok=True)
            file_path = os.path.join(photo_dir, unique_filename)
            photo_file.save(file_path)

            # Store the relative path in the database
            weapon.photo_path = f"images/weapons/{unique_filename}"

        db.session.commit()

        flash(f'Weapon {weapon.name} updated successfully!')
        return redirect(url_for('main.weapon', id=weapon.id))

    return render_template('edit_weapon.html', title=f'Edit {weapon.name}', weapon=weapon)

@main_bp.route('/weapon/add', methods=['GET', 'POST'])
@login_required
def add_weapon():
    if request.method == 'POST':
        name = request.form['name']
        type = request.form['type']
        caliber = request.form['caliber']
        description = request.form['description']

        # Initialize photo_path as None
        photo_path = None

        # Check if a photo was uploaded
        if 'photo' in request.files and request.files['photo'].filename != '':
            photo_file = request.files['photo']

            # Secure the filename and generate a unique name
            filename = secure_filename(photo_file.filename)
            unique_filename = f"{uuid.uuid4().hex}_{filename}"

            # Save the file
            photo_dir = os.path.join(current_app.static_folder, 'images/weapons')
            os.makedirs(photo_dir, exist_ok=True)
            file_path = os.path.join(photo_dir, unique_filename)
            photo_file.save(file_path)

            # Store the relative path in the database
            photo_path = f"images/weapons/{unique_filename}"

        weapon = Weapon(
            name=name,
            type=type,
            caliber=caliber,
            description=description,
            photo_path=photo_path
        )

        db.session.add(weapon)
        db.session.commit()

        flash(f'Weapon {name} added successfully!')
        return redirect(url_for('main.weapons'))

    return render_template('add_weapon.html', title='Add Weapon')

# Session routes
@main_bp.route('/sessions')
@login_required
def sessions():
    sessions = ShootingSession.query.order_by(ShootingSession.date.desc()).all()
    return render_template('sessions.html', title='Shooting Sessions', sessions=sessions)

@main_bp.route('/session/<int:id>')
@login_required
def session(id):
    session = ShootingSession.query.get_or_404(id)
    shots = Shot.query.filter_by(session_id=id).order_by(Shot.timestamp).all()
    return render_template('session_detail.html', title=f'Session {id}', session=session, shots=shots)

@main_bp.route('/session/start', methods=['GET', 'POST'])
@login_required
def start_session():
    if request.method == 'POST':
        session_type = request.form['session_type']
        weapon_id = request.form['weapon_id']
        distance = request.form['distance']
        target_type = request.form['target_type']
        weather_conditions = request.form['weather_conditions']
        notes = request.form['notes']

        # Handle individual session
        if session_type == 'individual':
            shooter_id = request.form['shooter_id']

            session = ShootingSession(
                shooter_id=shooter_id,
                weapon_id=weapon_id,
                distance=distance,
                target_type=target_type,
                weather_conditions=weather_conditions,
                notes=notes
            )

            db.session.add(session)
            db.session.commit()

            flash(f'Individual training session started successfully!')
            return redirect(url_for('main.session', id=session.id))

        # Handle group session
        elif session_type == 'group':
            group_id = request.form['group_id']
            group = ShooterGroup.query.get_or_404(group_id)

            # Get all shooters in the group
            shooters = Shooter.query.filter_by(group_id=group_id).all()

            if not shooters:
                flash(f'No soldiers found in the selected unit. Please add soldiers to the unit first.', 'warning')
                return redirect(url_for('main.start_session'))

            # Create a session for each shooter in the group
            created_sessions = []
            for shooter in shooters:
                session = ShootingSession(
                    shooter_id=shooter.id,
                    weapon_id=weapon_id,
                    distance=distance,
                    target_type=target_type,
                    weather_conditions=weather_conditions,
                    notes=f"Group training: {group.name}. {notes}"
                )

                db.session.add(session)
                created_sessions.append(session)

            db.session.commit()

            flash(f'Unit training started successfully for {len(created_sessions)} soldiers in {group.name}!')

            # Redirect to the first session
            if created_sessions:
                return redirect(url_for('main.session', id=created_sessions[0].id))
            else:
                return redirect(url_for('main.sessions'))

    shooters = Shooter.query.all()
    weapons = Weapon.query.all()
    groups = ShooterGroup.query.all()
    return render_template('start_session.html', title='Start Session', shooters=shooters, weapons=weapons, groups=groups)

# Camera routes
@camera_bp.route('/view')
@login_required
def view_cameras():
    # Get all shooters with assigned cameras
    assigned_shooters = Shooter.query.filter(Shooter.camera_id.isnot(None)).all()

    # Get all shooters for the assignment dropdown
    all_shooters = Shooter.query.order_by(Shooter.last_name, Shooter.first_name).all()

    # Create a dictionary mapping camera_id to shooter
    camera_assignments = {}
    for shooter in assigned_shooters:
        camera_assignments[shooter.camera_id] = shooter

    return render_template('cameras.html', title='Camera View',
                          camera_assignments=camera_assignments,
                          shooters=all_shooters)

@camera_bp.route('/settings', methods=['GET'])
@login_required
def camera_settings():
    """Camera settings page"""
    # Get camera status
    camera_status = camera_manager.get_camera_status()

    # Get camera count from config
    camera_count = current_app.config['CAMERA_COUNT']

    # Get camera names (placeholder for now)
    camera_names = {}
    for i in range(1, camera_count + 1):
        camera_names[i] = f"Camera {i}"

    return render_template('camera_settings.html',
                          title='Camera Settings',
                          camera_status=camera_status,
                          camera_count=camera_count,
                          camera_names=camera_names)

@camera_bp.route('/settings/save', methods=['POST'])
@login_required
def save_camera_settings():
    """Save camera settings"""
    # Get camera count from config
    camera_count = current_app.config['CAMERA_COUNT']

    # Create a new .env file with updated camera URLs
    env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')

    # Read existing .env file
    with open(env_path, 'r') as f:
        env_lines = f.readlines()

    # Update camera URLs
    new_env_lines = []
    camera_url_lines = {}

    # First pass: collect all camera URL lines and their indices
    for i, line in enumerate(env_lines):
        if line.startswith('CAMERA_URL_'):
            camera_num = int(line.split('=')[0].replace('CAMERA_URL_', ''))
            camera_url_lines[camera_num] = i
        else:
            new_env_lines.append(line)

    # Add camera count
    if 'CAMERA_COUNT=' not in ''.join(new_env_lines):
        new_env_lines.append(f'CAMERA_COUNT={camera_count}\n')

    # Add updated camera URLs
    for i in range(1, camera_count + 1):
        camera_url = request.form.get(f'camera_url_{i}', '')
        new_env_lines.append(f'CAMERA_URL_{i}={camera_url}\n')

    # Write updated .env file
    with open(env_path, 'w') as f:
        f.writelines(new_env_lines)

    # Reload environment variables
    from dotenv import load_dotenv
    load_dotenv(env_path, override=True)

    # Update the app configuration with new camera URLs
    new_camera_urls = []
    for i in range(1, camera_count + 1):
        camera_url = request.form.get(f'camera_url_{i}', '')
        if camera_url:
            new_camera_urls.append(camera_url)
        else:
            new_camera_urls.append(f'http://camera{i}.local/video')

    # Update the current app config
    current_app.config['CAMERA_URLS'] = new_camera_urls

    # Reinitialize the camera manager with updated URLs
    camera_manager.init_app(current_app)

    flash('Camera settings saved successfully! Camera connections have been updated.', 'success')
    return redirect(url_for('camera_bp.camera_settings'))

@camera_bp.route('/test-connection', methods=['POST'])
@login_required
def test_camera_connection():
    """Test camera connection"""
    camera_id = request.form.get('camera_id', type=int)
    camera_url = request.form.get('camera_url', '')

    if not camera_url:
        return jsonify({'success': False, 'error': 'No camera URL provided'})

    try:
        # Create a temporary camera object
        temp_camera = Camera(camera_id, camera_url)

        # Try to connect and get a frame
        if temp_camera.connect():
            frame = temp_camera.get_frame()
            temp_camera.disconnect()

            if frame is not None:
                return jsonify({'success': True})
            else:
                return jsonify({'success': False, 'error': 'Connected but could not get video frame'})
        else:
            return jsonify({'success': False, 'error': 'Could not connect to camera'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@camera_bp.route('/stream/<int:camera_id>')
def video_feed(camera_id):
    """Video streaming route for a specific camera"""
    def generate():
        camera = camera_manager.get_camera(camera_id)
        if not camera:
            print(f"Camera {camera_id} not found in camera manager")
            return

        # Try to connect if not already connected
        if not camera.connected:
            print(f"Camera {camera_id} not connected, attempting to connect...")
            if not camera.connect():
                print(f"Failed to connect to camera {camera_id}")
                return

        retry_count = 0
        max_retries = 10

        while retry_count < max_retries:
            try:
                frame = camera.get_jpeg()
                if frame:
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
                    retry_count = 0  # Reset retry count on successful frame
                else:
                    retry_count += 1
                    print(f"No frame from camera {camera_id}, retry {retry_count}/{max_retries}")
                    # If no frame available, yield a small delay
                    import time
                    time.sleep(0.5)
            except Exception as e:
                print(f"Error getting frame from camera {camera_id}: {e}")
                retry_count += 1
                import time
                time.sleep(0.5)

    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@camera_bp.route('/status')
@login_required
def camera_status():
    """Get current status of all cameras"""
    status = camera_manager.get_camera_status()
    return jsonify(status)

@camera_bp.route('/assign-shooter', methods=['POST'])
@login_required
def assign_shooter():
    """Assign a shooter to a camera"""
    camera_id = request.form.get('camera_id', type=int)
    shooter_id = request.form.get('shooter_id')

    if not camera_id:
        flash('Invalid camera ID', 'danger')
        return redirect(url_for('camera_bp.view_cameras'))

    # If shooter_id is empty, unassign the camera
    if not shooter_id:
        # Find any shooter currently assigned to this camera
        current_assignment = Shooter.query.filter_by(camera_id=camera_id).first()
        if current_assignment:
            current_assignment.camera_id = None
            db.session.commit()
            flash(f'Camera {camera_id} has been unassigned', 'success')
        return redirect(url_for('camera_bp.view_cameras'))

    # Get the shooter
    shooter = Shooter.query.get_or_404(shooter_id)

    # Check if this camera is already assigned to another shooter
    current_assignment = Shooter.query.filter(Shooter.camera_id == camera_id, Shooter.id != shooter.id).first()
    if current_assignment:
        current_assignment.camera_id = None
        flash(f'Camera {camera_id} was reassigned from {current_assignment.full_name} to {shooter.full_name}', 'warning')

    # Check if this shooter is already assigned to another camera
    if shooter.camera_id and shooter.camera_id != camera_id:
        flash(f'{shooter.full_name} was previously assigned to Camera {shooter.camera_id}', 'info')

    # Assign the camera to the shooter
    shooter.camera_id = camera_id
    db.session.commit()

    flash(f'{shooter.full_name} has been assigned to Camera {camera_id}', 'success')
    return redirect(url_for('camera_bp.view_cameras'))

@camera_bp.route('/capture/<int:camera_id>/<int:session_id>')
@login_required
def capture_image(camera_id, session_id):
    """Capture an image from a specific camera and save it"""
    camera = camera_manager.get_camera(camera_id)
    if not camera:
        return jsonify({'success': False, 'error': 'Camera not found'})

    image_path = camera.save_image(session_id)
    if not image_path:
        return jsonify({'success': False, 'error': 'Failed to capture image'})

    # Create a new shot record
    shot = Shot(
        session_id=session_id,
        camera_id=camera_id,
        image_path=image_path
    )

    db.session.add(shot)
    db.session.commit()

    return jsonify({
        'success': True,
        'shot_id': shot.id,
        'image_path': image_path
    })

# Image Overlay Configuration Routes
@main_bp.route('/image-overlays')
@login_required
def image_overlays():
    """Image overlay configuration page"""
    # Get all configurations
    configs = ImageOverlayConfig.query.all()

    # Get active configuration (default or first one)
    active_config = ImageOverlayConfig.query.filter_by(is_default=True).first()
    if not active_config and configs:
        active_config = configs[0]
    if not active_config:
        # Create a default configuration if none exists
        active_config = ImageOverlayConfig(name="Default Configuration", is_default=True)
        db.session.add(active_config)
        db.session.commit()
        configs = [active_config]

    # Get a sample image for preview
    # Try to get a recent shot image, or use a default one
    sample_shot = Shot.query.filter(Shot.image_path.isnot(None)).order_by(Shot.timestamp.desc()).first()
    if sample_shot and sample_shot.image_path:
        preview_image = sample_shot.image_path
    else:
        preview_image = 'images/default_target.jpg'

    return render_template('image_overlays.html',
                          title='Image Overlay Settings',
                          configs=configs,
                          active_config=active_config,
                          preview_image=preview_image)

@main_bp.route('/image-overlays/<int:id>')
@login_required
def edit_overlay_config(id):
    """Edit specific overlay configuration"""
    # Get the requested configuration
    config = ImageOverlayConfig.query.get_or_404(id)

    # Get all configurations
    configs = ImageOverlayConfig.query.all()

    # Get a sample image for preview
    sample_shot = Shot.query.filter(Shot.image_path.isnot(None)).order_by(Shot.timestamp.desc()).first()
    if sample_shot and sample_shot.image_path:
        preview_image = sample_shot.image_path
    else:
        preview_image = 'images/default_target.jpg'

    return render_template('image_overlays.html',
                          title=f'Edit {config.name}',
                          configs=configs,
                          active_config=config,
                          preview_image=preview_image)

@main_bp.route('/image-overlays/add', methods=['POST'])
@login_required
def add_overlay_config():
    """Add a new overlay configuration"""
    name = request.form['name']
    is_default = 'is_default' in request.form

    # Check if name already exists
    existing = ImageOverlayConfig.query.filter_by(name=name).first()
    if existing:
        flash(f'Configuration with name "{name}" already exists.', 'danger')
        return redirect(url_for('main.image_overlays'))

    # Create new configuration
    config = ImageOverlayConfig(name=name, is_default=is_default)

    # If this is set as default, unset any other defaults
    if is_default:
        ImageOverlayConfig.query.filter_by(is_default=True).update({'is_default': False})

    db.session.add(config)
    db.session.commit()

    flash(f'Configuration "{name}" created successfully!', 'success')
    return redirect(url_for('main.edit_overlay_config', id=config.id))

@main_bp.route('/image-overlays/<int:id>/update', methods=['POST'])
@login_required
def update_overlay_config(id):
    """Update an existing overlay configuration"""
    config = ImageOverlayConfig.query.get_or_404(id)

    # Get form data
    name = request.form['name']
    is_default = 'is_default' in request.form

    # Check if name already exists (for a different config)
    existing = ImageOverlayConfig.query.filter(ImageOverlayConfig.name == name,
                                              ImageOverlayConfig.id != id).first()
    if existing:
        flash(f'Configuration with name "{name}" already exists.', 'danger')
        return redirect(url_for('main.edit_overlay_config', id=id))

    # Update basic settings
    config.name = name

    # Handle default status
    if is_default and not config.is_default:
        # Unset any other defaults
        ImageOverlayConfig.query.filter_by(is_default=True).update({'is_default': False})
        config.is_default = True
    elif not is_default and config.is_default:
        # Don't allow unsetting default if this is the only config
        if ImageOverlayConfig.query.count() == 1:
            flash('Cannot unset default status for the only configuration.', 'warning')
        else:
            config.is_default = False

    # Update text settings
    if 'font_family' in request.form:
        config.font_family = request.form['font_family']
    if 'font_size' in request.form:
        config.font_size = int(request.form['font_size'])
    if 'font_color' in request.form:
        config.font_color = request.form['font_color']
    if 'font_outline_color' in request.form:
        config.font_outline_color = request.form['font_outline_color']
    if 'font_outline_width' in request.form:
        config.font_outline_width = int(request.form['font_outline_width'])
    if 'text_position' in request.form:
        config.text_position = request.form['text_position']

    # Update marker settings
    if 'marker_size' in request.form:
        config.marker_size = int(request.form['marker_size'])
    if 'marker_color' in request.form:
        config.marker_color = request.form['marker_color']
    if 'marker_outline_color' in request.form:
        config.marker_outline_color = request.form['marker_outline_color']
    if 'marker_outline_width' in request.form:
        config.marker_outline_width = int(request.form['marker_outline_width'])
    if 'marker_style' in request.form:
        config.marker_style = request.form['marker_style']

    # Update scoring zone settings
    if 'show_scoring_zones' in request.form:
        config.show_scoring_zones = True
    else:
        config.show_scoring_zones = False
    if 'scoring_zone_color' in request.form:
        config.scoring_zone_color = request.form['scoring_zone_color']
    if 'scoring_zone_opacity' in request.form:
        config.scoring_zone_opacity = float(request.form['scoring_zone_opacity'])

    # Update information display settings
    config.show_shooter_info = 'show_shooter_info' in request.form
    config.show_session_info = 'show_session_info' in request.form
    config.show_timestamp = 'show_timestamp' in request.form
    config.show_score = 'show_score' in request.form
    config.show_coordinates = 'show_coordinates' in request.form

    # Update watermark settings
    if 'watermark_text' in request.form:
        config.watermark_text = request.form['watermark_text']
    if 'watermark_opacity' in request.form:
        config.watermark_opacity = float(request.form['watermark_opacity'])

    # Update background settings
    if 'background_color' in request.form:
        config.background_color = request.form['background_color']
    if 'background_opacity' in request.form:
        config.background_opacity = float(request.form['background_opacity'])

    db.session.commit()

    flash(f'Configuration "{name}" updated successfully!', 'success')
    return redirect(url_for('main.edit_overlay_config', id=id))

@main_bp.route('/image-overlays/<int:id>/preview', methods=['POST'])
@login_required
def preview_overlay_config(id):
    """Generate a preview for the overlay configuration"""
    config = ImageOverlayConfig.query.get_or_404(id)

    # Get a sample shot for preview
    sample_shot = Shot.query.filter(Shot.image_path.isnot(None)).order_by(Shot.timestamp.desc()).first()

    if not sample_shot:
        return jsonify({'success': False, 'error': 'No sample shot available for preview'})

    # Apply the form values to the config temporarily (without saving to DB)
    temp_config = ImageOverlayConfig(
        name=request.form.get('name', config.name),
        font_family=request.form.get('font_family', config.font_family),
        font_size=int(request.form.get('font_size', config.font_size)),
        font_color=request.form.get('font_color', config.font_color),
        font_outline_color=request.form.get('font_outline_color', config.font_outline_color),
        font_outline_width=int(request.form.get('font_outline_width', config.font_outline_width)),
        text_position=request.form.get('text_position', config.text_position),
        marker_size=int(request.form.get('marker_size', config.marker_size)),
        marker_color=request.form.get('marker_color', config.marker_color),
        marker_outline_color=request.form.get('marker_outline_color', config.marker_outline_color),
        marker_outline_width=int(request.form.get('marker_outline_width', config.marker_outline_width)),
        marker_style=request.form.get('marker_style', config.marker_style),
        show_scoring_zones='show_scoring_zones' in request.form,
        scoring_zone_color=request.form.get('scoring_zone_color', config.scoring_zone_color),
        scoring_zone_opacity=float(request.form.get('scoring_zone_opacity', config.scoring_zone_opacity)),
        show_shooter_info='show_shooter_info' in request.form,
        show_session_info='show_session_info' in request.form,
        show_timestamp='show_timestamp' in request.form,
        show_score='show_score' in request.form,
        show_coordinates='show_coordinates' in request.form,
        watermark_text=request.form.get('watermark_text', config.watermark_text),
        watermark_opacity=float(request.form.get('watermark_opacity', config.watermark_opacity)),
        background_color=request.form.get('background_color', config.background_color),
        background_opacity=float(request.form.get('background_opacity', config.background_opacity))
    )

    # Process the image with the temporary config
    processed_image_path = image_processor.process_shot_image(sample_shot.id, temp_config)

    if processed_image_path:
        return jsonify({
            'success': True,
            'preview_url': url_for('static', filename=processed_image_path)
        })
    else:
        return jsonify({'success': False, 'error': 'Failed to generate preview'})

@main_bp.route('/image-overlays/delete', methods=['POST'])
@login_required
def delete_overlay_config():
    """Delete an overlay configuration"""
    config_id = request.form['config_id']
    config = ImageOverlayConfig.query.get_or_404(config_id)

    # Don't allow deleting the default configuration
    if config.is_default:
        flash('Cannot delete the default configuration.', 'danger')
        return redirect(url_for('main.image_overlays'))

    # Don't allow deleting the only configuration
    if ImageOverlayConfig.query.count() == 1:
        flash('Cannot delete the only configuration.', 'danger')
        return redirect(url_for('main.image_overlays'))

    # Delete the configuration
    db.session.delete(config)
    db.session.commit()

    flash(f'Configuration "{config.name}" deleted successfully!', 'success')
    return redirect(url_for('main.image_overlays'))

# Reports routes
@main_bp.route('/reports')
@login_required
def reports():
    """Reports page"""
    # Get report type from query parameters
    report_type = request.args.get('report_type', 'summary')

    # Get all shooters
    shooters = Shooter.query.order_by(Shooter.last_name, Shooter.first_name).all()

    # Get all groups
    groups = ShooterGroup.query.all()

    # Get all weapons
    weapons = Weapon.query.all()

    # Calculate basic statistics
    total_sessions = ShootingSession.query.count()
    total_shots = Shot.query.count()

    # Calculate average score
    avg_score_result = db.session.query(func.avg(Shot.score)).first()
    avg_score = round(avg_score_result[0], 2) if avg_score_result[0] else 0

    # Calculate accuracy rate (shots with score > 7 / total shots)
    accuracy_count = Shot.query.filter(Shot.score > 7).count()
    accuracy_rate = round((accuracy_count / total_shots * 100), 1) if total_shots > 0 else 0

    # Get top shooters
    top_shooters = []
    for shooter in shooters[:10]:  # Limit to 10 shooters for initial display
        # Get shooter's sessions
        sessions = ShootingSession.query.filter_by(shooter_id=shooter.id).all()
        session_count = len(sessions)

        if session_count > 0:
            # Calculate average score
            shots = Shot.query.join(ShootingSession).filter(ShootingSession.shooter_id == shooter.id).all()
            if shots:
                scores = [shot.score for shot in shots]
                avg_score = round(sum(scores) / len(scores), 2)
                best_score = round(max(scores), 2)

                # Calculate improvement (random for demo)
                improvement = random.randint(-10, 30)

                # Determine performance level
                if avg_score >= 8.5:
                    performance = 'excellent'
                elif avg_score >= 7:
                    performance = 'good'
                elif avg_score >= 5:
                    performance = 'average'
                else:
                    performance = 'poor'

                top_shooters.append({
                    'id': shooter.id,
                    'full_name': shooter.full_name,
                    'army_number': shooter.army_number,
                    'group': shooter.group,
                    'session_count': session_count,
                    'avg_score': avg_score,
                    'best_score': best_score,
                    'improvement': improvement,
                    'performance': performance
                })

    # Sort top shooters by average score
    top_shooters = sorted(top_shooters, key=lambda x: x['avg_score'], reverse=True)

    # Prepare statistics for the template
    stats = {
        'total_sessions': total_sessions,
        'total_shots': total_shots,
        'avg_score': avg_score,
        'accuracy': accuracy_rate
    }

    # Prepare report title and description based on report type
    report_info = get_report_info(report_type)

    # Get report-specific data
    report_data = generate_report_data(report_type, shooters, groups, weapons)

    return render_template('reports.html',
                          title=f'{report_info["title"]} - Performance Reports',
                          report_type=report_type,
                          report_info=report_info,
                          shooters=shooters,
                          groups=groups,
                          weapons=weapons,
                          stats=stats,
                          top_shooters=top_shooters,
                          report_data=report_data)

def get_report_info(report_type):
    """Get report title and description based on report type"""
    reports = {
        'summary': {
            'title': 'Summary Report',
            'description': 'Overview of all shooting range activities and performance metrics',
            'icon': 'clipboard-list'
        },
        'individual_performance': {
            'title': 'Individual Performance',
            'description': 'Detailed analysis of individual shooter performance metrics',
            'icon': 'user'
        },
        'shooter_comparison': {
            'title': 'Shooter Comparison',
            'description': 'Compare performance metrics between different shooters',
            'icon': 'users'
        },
        'improvement_tracking': {
            'title': 'Improvement Tracking',
            'description': 'Track shooter improvement over time with trend analysis',
            'icon': 'chart-line'
        },
        'unit_performance': {
            'title': 'Unit Performance',
            'description': 'Performance metrics for military units and groups',
            'icon': 'shield-alt'
        },
        'unit_comparison': {
            'title': 'Unit Comparison',
            'description': 'Compare performance metrics between different units',
            'icon': 'balance-scale'
        },
        'weapon_analysis': {
            'title': 'Weapon Analysis',
            'description': 'Analysis of weapon performance and accuracy metrics',
            'icon': 'crosshairs'
        },
        'equipment_effectiveness': {
            'title': 'Equipment Effectiveness',
            'description': 'Evaluate the effectiveness of different equipment configurations',
            'icon': 'tools'
        }
    }

    return reports.get(report_type, reports['summary'])

def generate_report_data(report_type, shooters, groups, weapons):
    """Generate report-specific data based on report type"""
    if report_type == 'individual_performance':
        return generate_individual_performance_data(shooters)
    elif report_type == 'shooter_comparison':
        return generate_shooter_comparison_data(shooters)
    elif report_type == 'improvement_tracking':
        return generate_improvement_tracking_data(shooters)
    elif report_type == 'unit_performance':
        return generate_unit_performance_data(groups)
    elif report_type == 'unit_comparison':
        return generate_unit_comparison_data(groups)
    elif report_type == 'weapon_analysis':
        return generate_weapon_analysis_data(weapons)
    elif report_type == 'equipment_effectiveness':
        return generate_equipment_effectiveness_data(weapons)
    else:  # summary
        return generate_summary_data(shooters, groups, weapons)

def generate_individual_performance_data(shooters):
    """Generate data for individual performance report"""
    # For demo purposes, we'll return some sample data
    return {
        'shooter_data': get_top_performers(shooters, 10),
        'performance_by_distance': {
            'labels': ['10m', '25m', '50m', '100m', '200m', '300m'],
            'data': [8.7, 8.2, 7.5, 6.8, 5.9, 4.5]
        },
        'performance_by_weapon': {
            'labels': ['Rifle', 'Pistol', 'Shotgun', 'Sniper Rifle'],
            'data': [7.8, 6.9, 7.2, 8.5]
        }
    }

def generate_shooter_comparison_data(shooters):
    """Generate data for shooter comparison report"""
    # Get top performers
    top_performers = get_top_performers(shooters, 5)

    return {
        'comparison_data': {
            'labels': [s['full_name'] for s in top_performers],
            'accuracy': [s['accuracy'] for s in top_performers],
            'consistency': [s['consistency'] for s in top_performers],
            'improvement': [s['improvement'] for s in top_performers],
            'avg_score': [s['avg_score'] for s in top_performers]
        }
    }

def generate_improvement_tracking_data(shooters):
    """Generate data for improvement tracking report"""
    # For demo purposes, we'll return some sample data
    return {
        'improvement_over_time': {
            'labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'datasets': [
                {
                    'label': 'Average Score',
                    'data': [6.2, 6.5, 7.1, 7.3, 7.8, 8.2]
                },
                {
                    'label': 'Accuracy Rate',
                    'data': [65, 68, 72, 75, 79, 82]
                }
            ]
        },
        'top_improvers': get_top_improvers(shooters, 5)
    }

def generate_unit_performance_data(groups):
    """Generate data for unit performance report"""
    # For demo purposes, we'll return some sample data
    return {
        'unit_performance': {
            'labels': [g.name for g in groups[:5]],
            'avg_score': [random.uniform(6.5, 8.5) for _ in range(min(5, len(groups)))],
            'accuracy': [random.uniform(65, 85) for _ in range(min(5, len(groups)))],
            'sessions': [random.randint(10, 50) for _ in range(min(5, len(groups)))]
        }
    }

def generate_unit_comparison_data(groups):
    """Generate data for unit comparison report"""
    # For demo purposes, we'll return some sample data
    return {
        'comparison_data': {
            'labels': [g.name for g in groups[:5]],
            'avg_score': [random.uniform(6.5, 8.5) for _ in range(min(5, len(groups)))],
            'accuracy': [random.uniform(65, 85) for _ in range(min(5, len(groups)))],
            'improvement': [random.uniform(-5, 25) for _ in range(min(5, len(groups)))],
            'consistency': [random.uniform(60, 90) for _ in range(min(5, len(groups)))]
        }
    }

def generate_weapon_analysis_data(weapons):
    """Generate data for weapon analysis report"""
    # For demo purposes, we'll return some sample data
    return {
        'weapon_performance': {
            'labels': [w.name for w in weapons[:5]],
            'avg_score': [random.uniform(6.5, 8.5) for _ in range(min(5, len(weapons)))],
            'accuracy': [random.uniform(65, 85) for _ in range(min(5, len(weapons)))],
            'reliability': [random.uniform(70, 95) for _ in range(min(5, len(weapons)))]
        }
    }

def generate_equipment_effectiveness_data(weapons):
    """Generate data for equipment effectiveness report"""
    # For demo purposes, we'll return some sample data
    return {
        'equipment_effectiveness': {
            'labels': ['Standard Sight', 'Red Dot', 'Holographic', 'Scope 4x', 'Scope 8x'],
            'accuracy': [75, 82, 85, 88, 90],
            'acquisition_time': [1.2, 0.8, 0.7, 1.5, 2.0],
            'effective_range': [50, 75, 100, 300, 500]
        }
    }

def generate_summary_data(shooters, groups, weapons):
    """Generate data for summary report"""
    # For demo purposes, we'll return some sample data
    return {
        'top_performers': get_top_performers(shooters, 5),
        'top_units': get_top_units(groups, 3),
        'top_weapons': get_top_weapons(weapons, 3),
        'recent_improvements': {
            'labels': ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            'data': [7.2, 7.5, 7.8, 8.1]
        }
    }

def get_top_performers(shooters, limit=5):
    """Get top performing shooters with detailed metrics"""
    top_performers = []

    for shooter in shooters:
        # Get shooter's sessions
        sessions = ShootingSession.query.filter_by(shooter_id=shooter.id).all()
        session_count = len(sessions)

        if session_count > 0:
            # Get all shots for this shooter
            shots = Shot.query.join(ShootingSession).filter(ShootingSession.shooter_id == shooter.id).all()

            if shots:
                # Calculate metrics
                scores = [shot.score for shot in shots]
                avg_score = round(sum(scores) / len(scores), 2)
                best_score = round(max(scores), 2)
                accuracy = round(len([s for s in scores if s > 7]) / len(scores) * 100, 1)
                consistency = round(100 - (max(scores) - min(scores)) * 10, 1)
                improvement = random.randint(-10, 30)  # Random for demo

                # Determine performance level
                if avg_score >= 8.5:
                    performance = 'excellent'
                elif avg_score >= 7:
                    performance = 'good'
                elif avg_score >= 5:
                    performance = 'average'
                else:
                    performance = 'poor'

                top_performers.append({
                    'id': shooter.id,
                    'full_name': shooter.full_name,
                    'army_number': shooter.army_number,
                    'group_name': shooter.group.name if shooter.group else 'N/A',
                    'session_count': session_count,
                    'shot_count': len(shots),
                    'avg_score': avg_score,
                    'best_score': best_score,
                    'accuracy': accuracy,
                    'consistency': consistency,
                    'improvement': improvement,
                    'performance': performance
                })

    # Sort by average score and limit
    return sorted(top_performers, key=lambda x: x['avg_score'], reverse=True)[:limit]

def get_top_improvers(shooters, limit=5):
    """Get shooters with the most improvement"""
    improvers = []

    for shooter in shooters:
        # Get shooter's sessions
        sessions = ShootingSession.query.filter_by(shooter_id=shooter.id).all()

        if len(sessions) >= 2:
            # For demo, generate random improvement
            improvement = random.randint(5, 35)

            improvers.append({
                'id': shooter.id,
                'full_name': shooter.full_name,
                'army_number': shooter.army_number,
                'group_name': shooter.group.name if shooter.group else 'N/A',
                'improvement': improvement,
                'initial_score': round(random.uniform(4.5, 6.5), 2),
                'current_score': round(random.uniform(7.0, 9.0), 2)
            })

    # Sort by improvement and limit
    return sorted(improvers, key=lambda x: x['improvement'], reverse=True)[:limit]

def get_top_units(groups, limit=3):
    """Get top performing units"""
    top_units = []

    for group in groups:
        # Get all shooters in this group
        shooters = Shooter.query.filter_by(group_id=group.id).all()

        if shooters:
            # For demo, generate random metrics
            avg_score = round(random.uniform(6.5, 8.5), 2)
            accuracy = round(random.uniform(65, 85), 1)

            top_units.append({
                'id': group.id,
                'name': group.name,
                'shooter_count': len(shooters),
                'avg_score': avg_score,
                'accuracy': accuracy,
                'performance': 'excellent' if avg_score >= 8.5 else 'good' if avg_score >= 7 else 'average'
            })

    # Sort by average score and limit
    return sorted(top_units, key=lambda x: x['avg_score'], reverse=True)[:limit]

def get_top_weapons(weapons, limit=3):
    """Get top performing weapons"""
    top_weapons = []

    for weapon in weapons:
        # Get all sessions with this weapon
        sessions = ShootingSession.query.filter_by(weapon_id=weapon.id).all()

        if sessions:
            # For demo, generate random metrics
            avg_score = round(random.uniform(6.5, 8.5), 2)
            accuracy = round(random.uniform(65, 85), 1)

            top_weapons.append({
                'id': weapon.id,
                'name': weapon.name,
                'type': weapon.type,
                'session_count': len(sessions),
                'avg_score': avg_score,
                'accuracy': accuracy
            })

    # Sort by average score and limit
    return sorted(top_weapons, key=lambda x: x['avg_score'], reverse=True)[:limit]

@main_bp.route('/api/reports')
@login_required
def api_reports():
    """API endpoint for report data"""
    report_type = request.args.get('type', 'individual')
    shooter_id = request.args.get('shooter_id', '')
    group_id = request.args.get('group_id', '')
    date_range = request.args.get('date_range', '30')

    # Calculate date range
    if date_range != 'all':
        days = int(date_range)
        start_date = datetime.utcnow() - timedelta(days=days)
    else:
        start_date = datetime(2000, 1, 1)  # A long time ago

    # Build query filters
    filters = [ShootingSession.date >= start_date]

    if shooter_id:
        filters.append(ShootingSession.shooter_id == int(shooter_id))

    if group_id:
        # Get all shooters in the group
        group_shooters = Shooter.query.filter_by(group_id=int(group_id)).all()
        shooter_ids = [s.id for s in group_shooters]
        if shooter_ids:
            filters.append(ShootingSession.shooter_id.in_(shooter_ids))

    # Get sessions based on filters
    sessions = ShootingSession.query.filter(*filters).all()

    # Get shots for these sessions
    session_ids = [s.id for s in sessions]
    shots = Shot.query.filter(Shot.session_id.in_(session_ids)).all()

    # Prepare data based on report type
    if report_type == 'individual':
        data = prepare_individual_report(sessions, shots, shooter_id)
    elif report_type == 'group':
        data = prepare_group_report(sessions, shots, group_id)
    elif report_type == 'weapon':
        data = prepare_weapon_report(sessions, shots)
    else:  # trend
        data = prepare_trend_report(sessions, shots)

    return jsonify(data)

# Helper functions for report generation
def prepare_individual_report(sessions, shots, shooter_id):
    """Prepare data for individual shooter report"""
    # Performance over time
    performance_data = {'labels': [], 'data': []}

    # Group sessions by date (month)
    sessions_by_month = {}
    for session in sessions:
        month_key = session.date.strftime('%Y-%m')
        if month_key not in sessions_by_month:
            sessions_by_month[month_key] = []
        sessions_by_month[month_key].append(session)

    # Sort months
    sorted_months = sorted(sessions_by_month.keys())

    # Calculate average score per month
    for month in sorted_months:
        month_sessions = sessions_by_month[month]
        month_session_ids = [s.id for s in month_sessions]
        month_shots = [s for s in shots if s.session_id in month_session_ids]

        if month_shots:
            avg_score = sum(s.score for s in month_shots) / len(month_shots)
            performance_data['labels'].append(datetime.strptime(month, '%Y-%m').strftime('%b %Y'))
            performance_data['data'].append(round(avg_score, 2))

    # Score distribution
    score_ranges = {'0-2': 0, '2-4': 0, '4-6': 0, '6-8': 0, '8-10': 0}
    for shot in shots:
        if shot.score < 2:
            score_ranges['0-2'] += 1
        elif shot.score < 4:
            score_ranges['2-4'] += 1
        elif shot.score < 6:
            score_ranges['4-6'] += 1
        elif shot.score < 8:
            score_ranges['6-8'] += 1
        else:
            score_ranges['8-10'] += 1

    # Prepare detailed performance data
    detailed_performance = []
    if shooter_id:
        shooter = Shooter.query.get(int(shooter_id))
        if shooter:
            session_count = len([s for s in sessions if s.shooter_id == shooter.id])
            shooter_shots = [s for s in shots if s.session.shooter_id == shooter.id]

            if shooter_shots:
                avg_score = sum(s.score for s in shooter_shots) / len(shooter_shots)
                best_score = max(s.score for s in shooter_shots)

                # Calculate improvement (random for demo)
                improvement = random.randint(-10, 30)

                # Determine performance level
                if avg_score >= 8.5:
                    performance = 'excellent'
                elif avg_score >= 7:
                    performance = 'good'
                elif avg_score >= 5:
                    performance = 'average'
                else:
                    performance = 'poor'

                detailed_performance.append({
                    'full_name': shooter.full_name,
                    'army_number': shooter.army_number,
                    'group_name': shooter.group.name if shooter.group else 'N/A',
                    'session_count': session_count,
                    'avg_score': round(avg_score, 2),
                    'best_score': round(best_score, 2),
                    'improvement': improvement,
                    'performance': performance
                })

    # Calculate statistics
    total_sessions = len(sessions)
    total_shots = len(shots)
    avg_score = round(sum(s.score for s in shots) / len(shots), 2) if shots else 0
    accuracy = round(len([s for s in shots if s.score > 7]) / len(shots) * 100, 1) if shots else 0

    return {
        'performance_over_time': performance_data,
        'score_distribution': {
            'labels': list(score_ranges.keys()),
            'data': list(score_ranges.values())
        },
        'top_performers': {
            'labels': [d['full_name'] for d in detailed_performance],
            'data': [d['avg_score'] for d in detailed_performance]
        },
        'weapon_comparison': {
            'labels': ['Rifle', 'Pistol', 'Shotgun', 'Sniper Rifle', 'Assault Rifle'],
            'data': [random.uniform(6, 9) for _ in range(5)]  # Random data for demo
        },
        'detailed_performance': detailed_performance,
        'stats': {
            'total_sessions': total_sessions,
            'total_shots': total_shots,
            'avg_score': avg_score,
            'accuracy': accuracy
        }
    }

def prepare_group_report(sessions, shots, group_id):
    """Prepare data for group comparison report"""
    # Similar to individual report but with group focus
    # For demo, we'll return similar data structure
    return prepare_individual_report(sessions, shots, None)

def prepare_weapon_report(sessions, shots):
    """Prepare data for weapon analysis report"""
    # Similar to individual report but with weapon focus
    # For demo, we'll return similar data structure
    return prepare_individual_report(sessions, shots, None)

def prepare_trend_report(sessions, shots):
    """Prepare data for trend analysis report"""
    # Similar to individual report but with trend focus
    # For demo, we'll return similar data structure
    return prepare_individual_report(sessions, shots, None)

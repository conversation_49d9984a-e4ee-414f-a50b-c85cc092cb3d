{% extends "base.html" %}

{% block title %}{{ weapon.name }} - Shooting Range Camera System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>{{ weapon.name }}</h1>
        <p class="lead">Weapon Details</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('main.weapons') }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Weapons
        </a>
        <a href="{{ url_for('main.edit_weapon', id=weapon.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit Weapon
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Weapon Information</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <img src="{{ url_for('static', filename=weapon.photo_url) }}" alt="{{ weapon.name }}" class="img-fluid rounded" style="max-height: 200px;">
                </div>
                
                <table class="table">
                    <tr>
                        <th>Name:</th>
                        <td>{{ weapon.name }}</td>
                    </tr>
                    <tr>
                        <th>Type:</th>
                        <td>{{ weapon.type }}</td>
                    </tr>
                    <tr>
                        <th>Caliber:</th>
                        <td>{{ weapon.caliber }}</td>
                    </tr>
                    <tr>
                        <th>Sessions:</th>
                        <td>{{ weapon.shooting_sessions.count() }}</td>
                    </tr>
                </table>
                
                {% if weapon.description %}
                <div class="mt-3">
                    <h6>Description:</h6>
                    <p>{{ weapon.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Shooting Sessions</h5>
            </div>
            <div class="card-body">
                {% if sessions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Shooter</th>
                                <th>Distance</th>
                                <th>Shots</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in sessions %}
                            <tr>
                                <td>{{ session.date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <a href="{{ url_for('main.shooter', id=session.shooter.id) }}">
                                        {{ session.shooter.full_name }}
                                    </a>
                                </td>
                                <td>{{ session.distance }} m</td>
                                <td>{{ session.shots.count() }}</td>
                                <td>
                                    <a href="{{ url_for('main.session', id=session.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No shooting sessions found for this weapon.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('main.start_session') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New Session
                </a>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Performance Statistics</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Statistics will be available after completing shooting sessions.
                </div>
                
                <!-- Placeholder for future statistics charts -->
                <div class="row text-center">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Average Score</h5>
                                <p class="display-4">--</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Total Sessions</h5>
                                <p class="display-4">{{ sessions|length }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

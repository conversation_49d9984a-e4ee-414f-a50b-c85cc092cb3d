{% extends "base.html" %}

{% block title %}Shooting Sessions - Shooting Range Camera System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>Shooting Sessions</h1>
        <p class="lead">View and manage shooting sessions</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('main.start_session') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Start New Session
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Session List</h5>
            </div>
            <div class="card-body">
                {% if sessions %}
                <div class="table-responsive">
                    <table class="table table-hover datatable">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Shooter</th>
                                <th>Weapon</th>
                                <th>Distance</th>
                                <th>Target Type</th>
                                <th>Shots</th>
                                <th style="width: 100px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in sessions %}
                            <tr>
                                <td>{{ session.date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <a href="{{ url_for('main.shooter', id=session.shooter.id) }}">
                                        {{ session.shooter.full_name }}
                                    </a>
                                </td>
                                <td>{{ session.weapon.name }}</td>
                                <td>{{ session.distance }} m</td>
                                <td>{{ session.target_type }}</td>
                                <td>{{ session.shots.count() }}</td>
                                <td>
                                    <a href="{{ url_for('main.session', id=session.id) }}" class="btn btn-sm btn-info" title="View Session Details">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No shooting sessions found. <a href="{{ url_for('main.start_session') }}">Start a new session</a> to get started.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Session Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-check fa-3x mb-3 text-primary"></i>
                                <h5 class="card-title">Total Sessions</h5>
                                <p class="display-6">{{ sessions|length }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-friends fa-3x mb-3 text-success"></i>
                                <h5 class="card-title">Unique Shooters</h5>
                                <p class="display-6">
                                    {{ sessions|map(attribute='shooter_id')|unique|list|length }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-crosshairs fa-3x mb-3 text-danger"></i>
                                <h5 class="card-title">Unique Weapons</h5>
                                <p class="display-6">
                                    {{ sessions|map(attribute='weapon_id')|unique|list|length }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-bullseye fa-3x mb-3 text-warning"></i>
                                <h5 class="card-title">Total Shots</h5>
                                <p class="display-6">
                                    {% set total_shots = 0 %}
                                    {% for session in sessions %}
                                        {% set total_shots = total_shots + session.shots.count() %}
                                    {% endfor %}
                                    {{ total_shots }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends "base.html" %}

{% block title %}Performance Reports - Armed Forces Shooting Range{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
<style>
    .report-card {
        margin-bottom: 2rem;
    }

    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .filter-section {
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 5px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .stat-card {
        text-align: center;
        padding: 1.5rem;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--military-dark-green);
    }

    .stat-label {
        text-transform: uppercase;
        font-size: 0.9rem;
        color: var(--military-tan);
        letter-spacing: 1px;
    }

    .performance-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .performance-excellent {
        background-color: #28a745;
    }

    .performance-good {
        background-color: #17a2b8;
    }

    .performance-average {
        background-color: #ffc107;
    }

    .performance-poor {
        background-color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="military-badge">INTELLIGENCE DIVISION</div>
        <h1>{{ report_info.title|default('PERFORMANCE ANALYTICS') }}</h1>
        <p class="lead">{{ report_info.description|default('Comprehensive shooting performance analysis and reporting') }}</p>
        <div class="military-divider"></div>
    </div>
    <div class="col-md-4 text-end">
        <div class="dropdown mb-3">
            <button class="btn btn-primary dropdown-toggle" type="button" id="reportTypeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-file-alt me-2"></i> Select Report
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="reportTypeDropdown">
                <li><h6 class="dropdown-header">Shooter Reports</h6></li>
                <li><a class="dropdown-item" href="{{ url_for('main.reports', report_type='individual_performance') }}">
                    <i class="fas fa-user me-2"></i> Individual Performance
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('main.reports', report_type='shooter_comparison') }}">
                    <i class="fas fa-users me-2"></i> Shooter Comparison
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('main.reports', report_type='improvement_tracking') }}">
                    <i class="fas fa-chart-line me-2"></i> Improvement Tracking
                </a></li>

                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">Unit Reports</h6></li>
                <li><a class="dropdown-item" href="{{ url_for('main.reports', report_type='unit_performance') }}">
                    <i class="fas fa-shield-alt me-2"></i> Unit Performance
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('main.reports', report_type='unit_comparison') }}">
                    <i class="fas fa-balance-scale me-2"></i> Unit Comparison
                </a></li>

                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">Equipment Reports</h6></li>
                <li><a class="dropdown-item" href="{{ url_for('main.reports', report_type='weapon_analysis') }}">
                    <i class="fas fa-crosshairs me-2"></i> Weapon Analysis
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('main.reports', report_type='equipment_effectiveness') }}">
                    <i class="fas fa-tools me-2"></i> Equipment Effectiveness
                </a></li>

                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{{ url_for('main.reports', report_type='summary') }}">
                    <i class="fas fa-clipboard-list me-2"></i> Summary Report
                </a></li>
            </ul>
        </div>

        <div class="btn-group">
            <button type="button" class="btn btn-outline-primary" id="exportPdfBtn">
                <i class="fas fa-file-pdf me-2"></i> Export PDF
            </button>
            <button type="button" class="btn btn-outline-success" id="exportCsvBtn">
                <i class="fas fa-file-csv me-2"></i> Export CSV
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card filter-section">
            <div class="card-body">
                <form id="reportFilters">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="reportType" class="form-label">Report Type</label>
                            <select class="form-select" id="reportType" name="report_type">
                                <option value="individual">Individual Performance</option>
                                <option value="group">Group Comparison</option>
                                <option value="weapon">Weapon Analysis</option>
                                <option value="trend">Performance Trends</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="shooterSelect" class="form-label">Shooter</label>
                            <select class="form-select" id="shooterSelect" name="shooter_id">
                                <option value="">All Shooters</option>
                                {% for shooter in shooters %}
                                <option value="{{ shooter.id }}">{{ shooter.full_name }} ({{ shooter.army_number }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="groupSelect" class="form-label">Unit</label>
                            <select class="form-select" id="groupSelect" name="group_id">
                                <option value="">All Units</option>
                                {% for group in groups %}
                                <option value="{{ group.id }}">{{ group.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="dateRange" class="form-label">Date Range</label>
                            <select class="form-select" id="dateRange" name="date_range">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="180">Last 6 Months</option>
                                <option value="365">Last Year</option>
                                <option value="all">All Time</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 text-center">
                            <button type="button" id="generateReportBtn" class="btn btn-primary">
                                <i class="fas fa-chart-line me-2"></i> GENERATE REPORT
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Key Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="stat-value" id="totalSessionsStat">{{ stats.total_sessions }}</div>
            <div class="stat-label">Total Sessions</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="stat-value" id="totalShotsStat">{{ stats.total_shots }}</div>
            <div class="stat-label">Total Shots</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="stat-value" id="avgScoreStat">{{ stats.avg_score }}</div>
            <div class="stat-label">Average Score</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="stat-value" id="accuracyStat">{{ stats.accuracy }}%</div>
            <div class="stat-label">Accuracy Rate</div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="row">
    <!-- Performance Over Time -->
    <div class="col-md-6 mb-4">
        <div class="card report-card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">PERFORMANCE OVER TIME</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Score Distribution -->
    <div class="col-md-6 mb-4">
        <div class="card report-card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">SCORE DISTRIBUTION</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="scoreDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performers -->
    <div class="col-md-6 mb-4">
        <div class="card report-card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">TOP PERFORMERS</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="topPerformersChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Weapon Comparison -->
    <div class="col-md-6 mb-4">
        <div class="card report-card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">WEAPON COMPARISON</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="weaponComparisonChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Performance Table -->
<div class="row">
    <div class="col-12">
        <div class="card report-card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">DETAILED PERFORMANCE</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Shooter</th>
                                <th>Army #</th>
                                <th>Unit</th>
                                <th>Sessions</th>
                                <th>Avg. Score</th>
                                <th>Best Score</th>
                                <th>Improvement</th>
                                <th>Performance</th>
                            </tr>
                        </thead>
                        <tbody id="performanceTableBody">
                            {% for shooter in top_shooters %}
                            <tr>
                                <td>{{ shooter.full_name }}</td>
                                <td>{{ shooter.army_number }}</td>
                                <td>{{ shooter.group.name if shooter.group else 'N/A' }}</td>
                                <td>{{ shooter.session_count }}</td>
                                <td>{{ shooter.avg_score }}</td>
                                <td>{{ shooter.best_score }}</td>
                                <td>{{ shooter.improvement }}%</td>
                                <td>
                                    {% if shooter.performance == 'excellent' %}
                                    <span class="performance-indicator performance-excellent"></span> Excellent
                                    {% elif shooter.performance == 'good' %}
                                    <span class="performance-indicator performance-good"></span> Good
                                    {% elif shooter.performance == 'average' %}
                                    <span class="performance-indicator performance-average"></span> Average
                                    {% else %}
                                    <span class="performance-indicator performance-poor"></span> Needs Improvement
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script src="{{ url_for('static', filename='js/reports.js') }}"></script>
{% endblock %}

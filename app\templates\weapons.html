{% extends "base.html" %}

{% block title %}Weapons - Shooting Range Camera System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>Weapons</h1>
        <p class="lead">Manage weapons used in shooting sessions</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('main.add_weapon') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Weapon
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Weapon List</h5>
            </div>
            <div class="card-body">
                {% if weapons %}
                <div class="table-responsive">
                    <table class="table table-hover datatable">
                        <thead>
                            <tr>
                                <th style="width: 80px;">Photo</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Caliber</th>
                                <th>Description</th>
                                <th>Sessions</th>
                                <th style="width: 100px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for weapon in weapons %}
                            <tr>
                                <td>
                                    <img src="{{ url_for('static', filename=weapon.photo_url) }}" alt="{{ weapon.name }}"
                                         class="img-thumbnail profile-image-small">
                                </td>
                                <td>{{ weapon.name }}</td>
                                <td>{{ weapon.type }}</td>
                                <td>{{ weapon.caliber }}</td>
                                <td>{{ weapon.description }}</td>
                                <td>{{ weapon.shooting_sessions.count() }}</td>
                                <td>
                                    <a href="{{ url_for('main.weapon', id=weapon.id) }}" class="btn btn-sm btn-info" title="View Weapon Details">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No weapons found. <a href="{{ url_for('main.add_weapon') }}">Add a weapon</a> to get started.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Weapon Categories</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-crosshairs fa-3x mb-3 text-primary"></i>
                                <h5 class="card-title">Rifles</h5>
                                <p class="card-text">
                                    {{ weapons|selectattr('type', 'equalto', 'Rifle')|list|length }} weapons
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-gun fa-3x mb-3 text-danger"></i>
                                <h5 class="card-title">Pistols</h5>
                                <p class="card-text">
                                    {{ weapons|selectattr('type', 'equalto', 'Pistol')|list|length }} weapons
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-bullseye fa-3x mb-3 text-success"></i>
                                <h5 class="card-title">Shotguns</h5>
                                <p class="card-text">
                                    {{ weapons|selectattr('type', 'equalto', 'Shotgun')|list|length }} weapons
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-bolt fa-3x mb-3 text-warning"></i>
                                <h5 class="card-title">Other</h5>
                                <p class="card-text">
                                    {{ weapons|rejectattr('type', 'in', ['Rifle', 'Pistol', 'Shotgun'])|list|length }} weapons
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

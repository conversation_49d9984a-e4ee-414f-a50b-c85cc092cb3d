/* Sidebar Menu Styles */

:root {
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
    --transition-speed: 0.3s;
}

/* Layout Styles */
body.sidebar-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding-left: var(--sidebar-width);
    transition: padding-left var(--transition-speed);
}

body.sidebar-layout.sidebar-collapsed {
    padding-left: var(--sidebar-collapsed-width);
}

body.sidebar-layout .main-content {
    flex: 1;
    width: 100%;
    padding: 20px;
}

body.sidebar-layout .top-navbar {
    display: none;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background-color: var(--military-dark-green);
    color: white;
    transition: width var(--transition-speed);
    z-index: 1030;
    overflow-x: hidden;
    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background-color: var(--military-darker-green);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.2rem;
}

.sidebar-header .logo img {
    height: 30px;
    margin-right: 10px;
}

.sidebar-header .logo-text {
    white-space: nowrap;
    transition: opacity var(--transition-speed);
}

.sidebar.collapsed .sidebar-header .logo-text {
    opacity: 0;
    width: 0;
    visibility: hidden;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 5px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin: 0;
    padding: 0;
}

.sidebar-menu .nav-link {
    display: flex;
    align-items: center;
    padding: 15px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s;
    white-space: nowrap;
}

.sidebar-menu .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar-menu .nav-link.active {
    background-color: var(--military-tan);
    color: var(--military-dark-green);
    font-weight: bold;
}

.sidebar-menu .nav-link i {
    min-width: 25px;
    margin-right: 10px;
    font-size: 1.1rem;
    text-align: center;
}

.sidebar-menu .nav-text {
    transition: opacity var(--transition-speed);
}

.sidebar.collapsed .sidebar-menu .nav-text {
    opacity: 0;
    width: 0;
    visibility: hidden;
}

.sidebar-divider {
    height: 1px;
    margin: 10px 15px;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 15px;
    background-color: var(--military-darker-green);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: center;
    transition: all var(--transition-speed);
}

.sidebar.collapsed .sidebar-footer {
    justify-content: center;
    padding: 15px 0;
}

.sidebar-footer .nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
}

.sidebar-footer .nav-link:hover {
    color: white;
}

.sidebar-footer .nav-link i {
    margin-right: 8px;
}

.sidebar.collapsed .sidebar-footer .nav-text {
    display: none;
}

/* Layout Toggle Button */
.layout-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1040;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--military-dark-green);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border: none;
    transition: all 0.2s;
}

.layout-toggle:hover {
    background-color: var(--military-tan);
    color: var(--military-dark-green);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    body.sidebar-layout {
        padding-left: 0;
    }
    
    body.sidebar-layout .sidebar {
        transform: translateX(-100%);
    }
    
    body.sidebar-layout.sidebar-visible .sidebar {
        transform: translateX(0);
    }
    
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1025;
        display: none;
    }
    
    body.sidebar-layout.sidebar-visible .sidebar-backdrop {
        display: block;
    }
}

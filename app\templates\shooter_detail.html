{% extends "base.html" %}

{% block title %}{{ shooter.full_name }} - Shooting Range Camera System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>{{ shooter.full_name }}</h1>
        <p class="lead">Shooter Profile</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('main.shooters') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Shooters
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Profile Information</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <img src="{{ url_for('static', filename=shooter.photo_url) }}" alt="{{ shooter.full_name }}" class="img-fluid rounded-circle profile-image">
                </div>

                <table class="table">
                    <tr>
                        <th>Name:</th>
                        <td>{{ shooter.full_name }}</td>
                    </tr>
                    <tr>
                        <th>Army Number:</th>
                        <td>{{ shooter.army_number }}</td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td>{{ shooter.email }}</td>
                    </tr>
                    <tr>
                        <th>Phone:</th>
                        <td>{{ shooter.phone or 'N/A' }}</td>
                    </tr>
                    <tr>
                        <th>Experience:</th>
                        <td>{{ shooter.experience_level }}</td>
                    </tr>
                    <tr>
                        <th>Group:</th>
                        <td>
                            {% if shooter.group %}
                            {{ shooter.group.name }}
                            {% else %}
                            <span class="text-muted">None</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Coach:</th>
                        <td>
                            {% if shooter.coach %}
                            {{ shooter.coach.username }}
                            {% else %}
                            <span class="text-muted">None</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Joined:</th>
                        <td>{{ shooter.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                </table>

                <div class="d-grid gap-2 mb-3">
                    <a href="{{ url_for('main.edit_shooter', id=shooter.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit Profile
                    </a>
                </div>

                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Camera Assignment</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ url_for('main.assign_camera', shooter_id=shooter.id) }}" method="post">
                            <div class="mb-3">
                                <label for="camera_id" class="form-label">Assigned Camera</label>
                                <select class="form-select" id="camera_id" name="camera_id">
                                    <option value="">None (Unassign)</option>
                                    {% for i in range(1, 9) %}
                                    <option value="{{ i }}" {% if shooter.camera_id == i %}selected{% endif %}>
                                        Camera {{ i }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-link"></i> Update Camera Assignment
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Shooting Sessions</h5>
            </div>
            <div class="card-body">
                {% if sessions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Weapon</th>
                                <th>Distance</th>
                                <th>Shots</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in sessions %}
                            <tr>
                                <td>{{ session.date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ session.weapon.name }}</td>
                                <td>{{ session.distance }} m</td>
                                <td>{{ session.shots.count() }}</td>
                                <td>
                                    <a href="{{ url_for('main.session', id=session.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No shooting sessions found for this shooter.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('main.start_session') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New Session
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Performance Statistics</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Statistics will be available after completing shooting sessions.
                </div>

                <!-- Placeholder for future statistics charts -->
                <div class="row text-center">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Average Score</h5>
                                <p class="display-4">--</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Total Sessions</h5>
                                <p class="display-4">{{ sessions|length }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

/**
 * Layout Switcher - <PERSON>les switching between top navbar and sidebar layouts
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const body = document.body;
    const sidebar = document.getElementById('sidebar');
    const topNavbar = document.getElementById('topNavbar');
    const layoutToggle = document.getElementById('layoutToggle');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebarBackdrop = document.getElementById('sidebarBackdrop');
    
    // Get saved layout preference from localStorage
    const savedLayout = localStorage.getItem('layoutPreference') || 'top';
    const savedSidebarState = localStorage.getItem('sidebarState') || 'expanded';
    
    // Apply saved layout preference
    setLayoutMode(savedLayout);
    
    // Apply saved sidebar state if in sidebar mode
    if (savedLayout === 'sidebar' && savedSidebarState === 'collapsed') {
        toggleSidebar();
    }
    
    // Layout toggle button click handler
    if (layoutToggle) {
        layoutToggle.addEventListener('click', function() {
            const currentLayout = body.classList.contains('sidebar-layout') ? 'sidebar' : 'top';
            const newLayout = currentLayout === 'top' ? 'sidebar' : 'top';
            
            setLayoutMode(newLayout);
            localStorage.setItem('layoutPreference', newLayout);
        });
    }
    
    // Sidebar toggle button click handler
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            toggleSidebar();
        });
    }
    
    // Backdrop click handler (for mobile)
    if (sidebarBackdrop) {
        sidebarBackdrop.addEventListener('click', function() {
            body.classList.remove('sidebar-visible');
        });
    }
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768 && body.classList.contains('sidebar-layout')) {
            body.classList.remove('sidebar-visible');
        }
    });
    
    /**
     * Set the layout mode (top or sidebar)
     * @param {string} mode - 'top' or 'sidebar'
     */
    function setLayoutMode(mode) {
        if (mode === 'sidebar') {
            body.classList.add('sidebar-layout');
            if (sidebar) sidebar.style.display = 'block';
            if (topNavbar) topNavbar.style.display = 'none';
            if (layoutToggle) {
                layoutToggle.innerHTML = '<i class="fas fa-bars"></i>';
                layoutToggle.setAttribute('title', 'Switch to Top Navigation');
            }
        } else {
            body.classList.remove('sidebar-layout', 'sidebar-collapsed', 'sidebar-visible');
            if (sidebar) sidebar.style.display = 'none';
            if (topNavbar) topNavbar.style.display = 'block';
            if (layoutToggle) {
                layoutToggle.innerHTML = '<i class="fas fa-columns"></i>';
                layoutToggle.setAttribute('title', 'Switch to Sidebar Navigation');
            }
        }
    }
    
    /**
     * Toggle sidebar expanded/collapsed state
     */
    function toggleSidebar() {
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
            body.classList.toggle('sidebar-collapsed');
            
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebarState', isCollapsed ? 'collapsed' : 'expanded');
            
            // Update toggle icon
            if (sidebarToggle) {
                sidebarToggle.innerHTML = isCollapsed ? 
                    '<i class="fas fa-angle-right"></i>' : 
                    '<i class="fas fa-angle-left"></i>';
            }
        }
    }
    
    /**
     * Set active menu item based on current URL
     */
    function setActiveMenuItem() {
        const currentPath = window.location.pathname;
        
        // Find all nav links
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            // Remove active class from all links
            link.classList.remove('active');
            
            // Skip dropdown toggles
            if (link.classList.contains('dropdown-toggle')) return;
            
            // Get the href attribute
            const href = link.getAttribute('href');
            
            // Skip if no href or it's a "#" link
            if (!href || href === '#') return;
            
            // Check if the current path starts with the link's href
            // This handles both exact matches and subpaths
            if (currentPath === href || 
                (href !== '/' && currentPath.startsWith(href))) {
                link.classList.add('active');
            }
        });
    }
    
    // Set active menu item on page load
    setActiveMenuItem();
});

{% extends "base.html" %}

{% block title %}Add Shooter - Shooting Range Camera System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1>Add Shooter</h1>
        <p class="lead">Create a new shooter profile</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Shooter Information</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('main.add_shooter') }}" method="post" enctype="multipart/form-data">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="army_number" class="form-label">Army Number</label>
                        <input type="text" class="form-control" id="army_number" name="army_number" required>
                        <div class="form-text">Military ID or service number</div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="phone" name="phone">
                    </div>

                    <div class="mb-3">
                        <label for="experience_level" class="form-label">Experience Level</label>
                        <select class="form-select" id="experience_level" name="experience_level" required>
                            <option value="" selected disabled>Select experience level</option>
                            <option value="Beginner">Beginner</option>
                            <option value="Intermediate">Intermediate</option>
                            <option value="Advanced">Advanced</option>
                            <option value="Expert">Expert</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="group_id" class="form-label">Shooter Group</label>
                        <select class="form-select" id="group_id" name="group_id">
                            <option value="">None</option>
                            {% for group in groups %}
                            <option value="{{ group.id }}">{{ group.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="photo" class="form-label">Profile Photo</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                            <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#webcamModal">
                                <i class="fas fa-camera"></i> Use Webcam
                            </button>
                        </div>
                        <div class="form-text">Optional: Upload a profile photo or capture one with your webcam</div>
                        <div class="mt-2">
                            <img id="photo-preview" class="img-thumbnail d-none photo-preview" alt="Photo preview">
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('main.shooters') }}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Add Shooter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Webcam Modal -->
<div class="modal fade" id="webcamModal" tabindex="-1" aria-labelledby="webcamModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="webcamModalLabel">Capture Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger" id="webcam-error"></div>

                <div class="text-center">
                    <!-- Video stream -->
                    <video id="webcam-video" autoplay></video>

                    <!-- Captured photo will be shown here -->
                    <img id="captured-photo" alt="Captured photo">

                    <!-- Canvas for processing the image (hidden) -->
                    <canvas id="webcam-canvas"></canvas>
                </div>

                <div class="d-flex justify-content-center mt-3" id="capture-controls">
                    <button type="button" id="capture-photo" class="btn btn-primary" disabled>
                        <i class="fas fa-camera"></i> Capture Photo
                    </button>
                </div>

                <div class="d-flex justify-content-center mt-3" id="review-controls">
                    <button type="button" id="retake-photo" class="btn btn-secondary me-2">
                        <i class="fas fa-redo"></i> Retake
                    </button>
                    <button type="button" id="use-photo" class="btn btn-success">
                        <i class="fas fa-check"></i> Use This Photo
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/webcam.js') }}"></script>
<script>
    // Preview uploaded image
    document.getElementById('photo').addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('photo-preview');
                preview.src = e.target.result;
                preview.classList.remove('d-none');
            };
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %}
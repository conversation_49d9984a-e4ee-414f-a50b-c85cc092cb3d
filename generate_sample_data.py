"""
Sample Data Generator for Shooting Range Application

This script generates sample data for the application, including:
- Shooter groups
- Shooters
- Weapons
- Shooting sessions
- Shots with random scores
"""

import os
import random
import datetime
from faker import Faker
from app import create_app, db
from app.models import User, ShooterGroup, Shooter, Weapon, ShootingSession, Shot

# Initialize Faker
fake = Faker()

# Configuration
NUM_GROUPS = 5
NUM_SHOOTERS = 30
NUM_WEAPONS = 10
NUM_SESSIONS_PER_SHOOTER = 5
NUM_SHOTS_PER_SESSION = 20
EXPERIENCE_LEVELS = ['Beginner', 'Intermediate', 'Advanced', 'Expert']
WEAPON_TYPES = ['Rifle', 'Pistol', 'Shotgun', 'Sniper Rifle', 'Assault Rifle']
WEAPON_CALIBERS = ['5.56mm', '7.62mm', '9mm', '.45 ACP', '.308', '.50 BMG', '12 Gauge']
TARGET_TYPES = ['Standard', 'Silhouette', 'Moving', 'Popup', 'Electronic']
WEATHER_CONDITIONS = ['Clear', 'Cloudy', 'Windy', 'Light Rain', 'Hot', 'Cold']
DISTANCES = [10, 25, 50, 100, 200, 300, 500]

def generate_sample_data():
    """Generate sample data for the application"""
    app = create_app()

    with app.app_context():
        print("Clearing existing data...")
        # Clear existing data (except users)
        try:
            Shot.query.delete()
            ShootingSession.query.delete()
            Shooter.query.delete()
            Weapon.query.delete()
            ShooterGroup.query.delete()
            db.session.commit()
        except Exception as e:
            print(f"Error clearing data: {e}")
            db.session.rollback()

        # Get or create admin user
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(username='admin', email='<EMAIL>')
            admin.set_password('admin123')
            admin.is_admin = True
            db.session.add(admin)
            db.session.commit()
            print("Created admin user (username: admin, password: admin123)")

        print("Generating shooter groups...")
        groups = []

        # Check existing groups
        existing_group_names = [g.name for g in ShooterGroup.query.all()]

        for i in range(NUM_GROUPS):
            # Generate a unique name
            while True:
                name = f"Unit {fake.military_ship()}-{random.randint(100, 999)}"
                if name not in existing_group_names:
                    break

            group = ShooterGroup(
                name=name,
                description=fake.paragraph(nb_sentences=2)
            )
            db.session.add(group)
            groups.append(group)
            existing_group_names.append(name)

        try:
            db.session.commit()
            print(f"Created {len(groups)} shooter groups")
        except Exception as e:
            print(f"Error creating groups: {e}")
            db.session.rollback()

        print("Generating weapons...")
        weapons = []

        # Check existing weapons
        existing_weapon_names = [w.name for w in Weapon.query.all()]

        for i in range(NUM_WEAPONS):
            # Generate a unique name
            while True:
                name = f"{fake.company()} {fake.word().capitalize()} {random.randint(1, 100)}"
                if name not in existing_weapon_names:
                    break

            weapon_type = random.choice(WEAPON_TYPES)
            weapon = Weapon(
                name=name,
                type=weapon_type,
                caliber=random.choice(WEAPON_CALIBERS),
                description=fake.paragraph(nb_sentences=1)
            )
            db.session.add(weapon)
            weapons.append(weapon)
            existing_weapon_names.append(name)

        try:
            db.session.commit()
            print(f"Created {len(weapons)} weapons")
        except Exception as e:
            print(f"Error creating weapons: {e}")
            db.session.rollback()

        print("Generating shooters...")
        shooters = []

        # Check existing army numbers
        existing_army_numbers = [s.army_number for s in Shooter.query.all()]
        existing_emails = [s.email for s in Shooter.query.all()]

        for i in range(NUM_SHOOTERS):
            first_name = fake.first_name()
            last_name = fake.last_name()

            # Generate unique army number
            while True:
                army_number = f"AF{random.randint(10000, 99999)}"
                if army_number not in existing_army_numbers:
                    break

            # Generate unique email
            while True:
                email = f"{first_name.lower()}.{last_name.lower()}{random.randint(1, 999)}@armymail.mil"
                if email not in existing_emails:
                    break

            shooter = Shooter(
                first_name=first_name,
                last_name=last_name,
                army_number=army_number,
                email=email,
                phone=fake.phone_number(),
                experience_level=random.choice(EXPERIENCE_LEVELS),
                group_id=random.choice(groups).id if groups and random.random() > 0.2 else None,
                coach_id=admin.id
            )
            db.session.add(shooter)
            shooters.append(shooter)
            existing_army_numbers.append(army_number)
            existing_emails.append(email)

        try:
            db.session.commit()
            print(f"Created {len(shooters)} shooters")
        except Exception as e:
            print(f"Error creating shooters: {e}")
            db.session.rollback()

        print("Generating shooting sessions and shots...")
        # Generate sessions and shots for each shooter
        session_count = 0
        shot_count = 0

        try:
            for shooter in shooters:
                # Create sessions for this shooter
                for _ in range(random.randint(1, NUM_SESSIONS_PER_SHOOTER)):
                    if not weapons:
                        continue

                    # Random date in the last 6 months
                    session_date = datetime.datetime.now() - datetime.timedelta(
                        days=random.randint(1, 180),
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )

                    session = ShootingSession(
                        shooter_id=shooter.id,
                        weapon_id=random.choice(weapons).id,
                        date=session_date,
                        distance=random.choice(DISTANCES),
                        target_type=random.choice(TARGET_TYPES),
                        weather_conditions=random.choice(WEATHER_CONDITIONS),
                        notes=fake.paragraph(nb_sentences=1) if random.random() > 0.5 else ""
                    )
                    db.session.add(session)

                    try:
                        db.session.commit()
                        session_count += 1

                        # Create shots for this session
                        shots_for_session = []
                        for shot_num in range(random.randint(5, NUM_SHOTS_PER_SESSION)):
                            # Skill-based scoring - better shooters get better scores on average
                            skill_factor = {
                                'Beginner': 0.5,
                                'Intermediate': 0.7,
                                'Advanced': 0.85,
                                'Expert': 0.95
                            }[shooter.experience_level]

                            # Random coordinates with skill factor
                            # Center of target is (0,0), shots are distributed around it
                            # More skilled shooters have shots closer to center
                            max_deviation = 10 * (1 - skill_factor)
                            x = random.uniform(-max_deviation, max_deviation)
                            y = random.uniform(-max_deviation, max_deviation)

                            # Calculate score based on distance from center (0,0)
                            # Score ranges from 0 to 10, with 10 being a perfect bullseye
                            distance_from_center = (x**2 + y**2)**0.5
                            score = max(0, 10 - distance_from_center)

                            # Add some randomness to the score
                            score = min(10, max(0, score + random.uniform(-1, 1)))

                            # Create timestamp for the shot (a few seconds after the previous shot)
                            shot_time = session_date + datetime.timedelta(seconds=shot_num * random.randint(15, 45))

                            shot = Shot(
                                session_id=session.id,
                                timestamp=shot_time,
                                x_coordinate=x,
                                y_coordinate=y,
                                score=round(score, 2),
                                camera_id=random.randint(1, 8)
                            )
                            db.session.add(shot)
                            shots_for_session.append(shot)

                        # Commit after all shots for this session
                        db.session.commit()
                        shot_count += len(shots_for_session)

                    except Exception as e:
                        print(f"Error creating shots for session: {e}")
                        db.session.rollback()

            print(f"Created {session_count} sessions with {shot_count} shots")

        except Exception as e:
            print(f"Error generating sessions and shots: {e}")
            db.session.rollback()

        print("Sample data generation complete!")
        print(f"Created {NUM_GROUPS} groups, {NUM_SHOOTERS} shooters, {NUM_WEAPONS} weapons")
        print(f"Each shooter has between 1 and {NUM_SESSIONS_PER_SHOOTER} sessions")
        print(f"Each session has between 5 and {NUM_SHOTS_PER_SESSION} shots")

if __name__ == "__main__":
    generate_sample_data()

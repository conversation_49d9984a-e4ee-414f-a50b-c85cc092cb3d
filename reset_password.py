"""
Password Reset Script for Shooting Range Application

This script allows resetting a user's password directly in the database.
Run it with: python reset_password.py
"""

import os
import sys
from getpass import getpass
from app import create_app, db
from app.models import User

def reset_password():
    """Reset a user's password"""
    app = create_app()
    
    with app.app_context():
        # List all users
        users = User.query.all()
        
        if not users:
            print("No users found in the database.")
            return
        
        print("\n=== Available Users ===")
        for i, user in enumerate(users, 1):
            print(f"{i}. {user.username} ({user.email})")
        
        try:
            choice = int(input("\nEnter the number of the user whose password you want to reset: "))
            if choice < 1 or choice > len(users):
                print("Invalid choice.")
                return
            
            selected_user = users[choice - 1]
            
            # Get and confirm new password
            while True:
                new_password = getpass("Enter new password: ")
                if len(new_password) < 8:
                    print("Password must be at least 8 characters long.")
                    continue
                
                confirm_password = getpass("Confirm new password: ")
                if new_password != confirm_password:
                    print("Passwords do not match. Please try again.")
                    continue
                
                break
            
            # Set the new password
            selected_user.set_password(new_password)
            db.session.commit()
            
            print(f"\nPassword for {selected_user.username} has been reset successfully!")
            print(f"You can now log in with the new password.")
            
        except ValueError:
            print("Please enter a valid number.")
        except Exception as e:
            print(f"An error occurred: {str(e)}")

if __name__ == "__main__":
    reset_password()

{% extends "base.html" %}

{% block title %}Edit Weapon - Shooting Range Camera System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1>Edit Weapon</h1>
        <p class="lead">Update weapon information</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Weapon Information</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('main.edit_weapon', id=weapon.id) }}" method="post" enctype="multipart/form-data">
                    <div class="row mb-4">
                        <div class="col-md-4 text-center">
                            <img src="{{ url_for('static', filename=weapon.photo_url) }}" alt="{{ weapon.name }}" class="img-fluid rounded mb-3" style="max-height: 150px;">
                            
                            <div class="mb-3">
                                <label for="photo" class="form-label">Update Photo</label>
                                <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                                <div class="form-text">Leave empty to keep current photo</div>
                            </div>
                        </div>
                        
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">Weapon Name</label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ weapon.name }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="type" class="form-label">Weapon Type</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="Pistol" {% if weapon.type == 'Pistol' %}selected{% endif %}>Pistol</option>
                                    <option value="Rifle" {% if weapon.type == 'Rifle' %}selected{% endif %}>Rifle</option>
                                    <option value="Shotgun" {% if weapon.type == 'Shotgun' %}selected{% endif %}>Shotgun</option>
                                    <option value="Revolver" {% if weapon.type == 'Revolver' %}selected{% endif %}>Revolver</option>
                                    <option value="Other" {% if weapon.type == 'Other' %}selected{% endif %}>Other</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="caliber" class="form-label">Caliber</label>
                                <input type="text" class="form-control" id="caliber" name="caliber" value="{{ weapon.caliber }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ weapon.description }}</textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('main.weapon', id=weapon.id) }}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

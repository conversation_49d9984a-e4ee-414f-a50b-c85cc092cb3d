{% extends "base.html" %}

{% block title %}User Profile - Armed Forces Shooting Range{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="military-badge">SECURE ACCESS</div>
        <h1>OFFICER PROFILE</h1>
        <p class="lead">Manage your account settings and security credentials</p>
        <div class="military-divider"></div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        {% endif %}
        {% endwith %}
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">PROFILE INFORMATION</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="profile-image-container mb-3">
                        <img src="{{ url_for('static', filename='images/default-avatar.jpg') }}" alt="{{ current_user.username }}" class="img-fluid rounded-circle profile-image">
                    </div>
                    <h4>{{ current_user.username }}</h4>
                    <p class="text-muted">{{ current_user.email }}</p>
                    <div class="badge bg-success mb-2">ACTIVE ACCOUNT</div>
                </div>

                <div class="military-divider"></div>

                <div class="profile-details">
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Username:</div>
                        <div class="col-7 fw-bold">{{ current_user.username }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Email:</div>
                        <div class="col-7">{{ current_user.email }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Joined:</div>
                        <div class="col-7">{{ current_user.created_at.strftime('%Y-%m-%d') if current_user.created_at else 'N/A' }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 text-muted">Last Login:</div>
                        <div class="col-7">{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else 'N/A' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">EDIT PROFILE</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('auth.update_profile') }}" method="post">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" value="{{ current_user.username }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ current_user.email }}" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Update Profile
                    </button>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">CHANGE PASSWORD</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('auth.change_password') }}" method="post">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <div class="form-text">Password must be at least 8 characters long and include letters and numbers.</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i> Change Password
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Password confirmation validation
        $('#confirm_password').on('keyup', function() {
            if ($('#new_password').val() == $('#confirm_password').val()) {
                $('#confirm_password').removeClass('is-invalid').addClass('is-valid');
            } else {
                $('#confirm_password').removeClass('is-valid').addClass('is-invalid');
            }
        });

        // Form validation
        $('form').on('submit', function(e) {
            const form = $(this);

            // If this is the password change form
            if (form.find('#new_password').length) {
                const newPassword = $('#new_password').val();
                const confirmPassword = $('#confirm_password').val();

                // Check if passwords match
                if (newPassword !== confirmPassword) {
                    e.preventDefault();
                    alert('Passwords do not match!');
                    return false;
                }

                // Check password strength
                if (newPassword.length < 8) {
                    e.preventDefault();
                    alert('Password must be at least 8 characters long!');
                    return false;
                }

                // Check if password contains letters and numbers
                if (!(/[A-Za-z]/.test(newPassword) && /[0-9]/.test(newPassword))) {
                    e.preventDefault();
                    alert('Password must include both letters and numbers!');
                    return false;
                }
            }
        });
    });
</script>
{% endblock %}

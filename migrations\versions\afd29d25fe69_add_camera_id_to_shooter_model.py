"""Add camera_id to Shooter model

Revision ID: afd29d25fe69
Revises: 
Create Date: 2025-05-17 13:27:59.497146

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'afd29d25fe69'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('shooter', schema=None) as batch_op:
        batch_op.add_column(sa.Column('camera_id', sa.Integer(), nullable=True))
        batch_op.alter_column('photo_path',
               existing_type=sa.TEXT(),
               type_=sa.String(length=256),
               existing_nullable=True)

    with op.batch_alter_table('weapon', schema=None) as batch_op:
        batch_op.alter_column('photo_path',
               existing_type=sa.TEXT(),
               type_=sa.String(length=256),
               existing_nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('weapon', schema=None) as batch_op:
        batch_op.alter_column('photo_path',
               existing_type=sa.String(length=256),
               type_=sa.TEXT(),
               existing_nullable=True)

    with op.batch_alter_table('shooter', schema=None) as batch_op:
        batch_op.alter_column('photo_path',
               existing_type=sa.String(length=256),
               type_=sa.TEXT(),
               existing_nullable=True)
        batch_op.drop_column('camera_id')

    # ### end Alembic commands ###

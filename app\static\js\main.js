// Main JavaScript file for the Shooting Range Camera System

// Initialize tooltips and popovers
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize Bootstrap popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});

// Camera functions
function refreshCameraStatus() {
    fetch('/camera/status')
        .then(response => response.json())
        .then(data => {
            // Update camera status indicators
            for (const [cameraId, status] of Object.entries(data)) {
                const statusElement = document.getElementById(`camera-status-${cameraId}`);
                if (statusElement) {
                    if (status.connected) {
                        statusElement.innerHTML = '<span class="badge bg-success">Connected</span>';
                    } else {
                        statusElement.innerHTML = '<span class="badge bg-danger">Disconnected</span>';
                    }
                }
            }
        })
        .catch(error => console.error('Error fetching camera status:', error));
}

// Session functions
function captureImage(cameraId, sessionId) {
    fetch(`/camera/capture/${cameraId}/${sessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showNotification('Image captured successfully!', 'success');
                
                // Add the new shot to the shots list if it exists
                const shotsList = document.getElementById('shots-list');
                if (shotsList) {
                    const shotItem = document.createElement('div');
                    shotItem.className = 'list-group-item';
                    shotItem.innerHTML = `
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">Shot #${data.shot_id}</h5>
                            <small>Just now</small>
                        </div>
                        <p class="mb-1">Camera: ${cameraId}</p>
                        <a href="#" class="btn btn-sm btn-primary view-shot-btn" data-shot-id="${data.shot_id}">
                            View Details
                        </a>
                    `;
                    shotsList.prepend(shotItem);
                }
            } else {
                // Show error message
                showNotification(`Failed to capture image: ${data.error}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Error capturing image:', error);
            showNotification('Error capturing image. Please try again.', 'danger');
        });
}

// Utility functions
function showNotification(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        // Create alert container if it doesn't exist
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.style.position = 'fixed';
        container.style.top = '20px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.getElementById('alert-container').appendChild(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return true;
    
    // Check all required fields
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            isValid = false;
            field.classList.add('is-invalid');
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

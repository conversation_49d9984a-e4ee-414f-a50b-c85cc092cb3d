# Shooting Range Camera System

A Python-based web application for capturing images from IP cameras in a shooting range, analyzing shooting results, and storing shooter data.

## Features

- Connect to 8 or more IP cameras
- Capture and analyze shooting results
- Store shooter data, weapons, and session information
- Track performance over time
- User authentication and management
- Responsive web interface using Flask and Bootstrap

## Requirements

- Python 3.8+
- Flask
- OpenCV
- SQLAlchemy
- Other dependencies listed in requirements.txt

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/shooting-range-camera-system.git
   cd shooting-range-camera-system
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - Windows:
     ```
     venv\Scripts\activate
     ```
   - macOS/Linux:
     ```
     source venv/bin/activate
     ```

4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

5. Configure the application:
   - Copy `.env.example` to `.env`
   - Edit `.env` to set your camera URLs and other configuration options

6. Initialize the database:
   ```
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

7. Run the application:
   ```
   flask run
   ```

## Configuration

Edit the `.env` file to configure:

- Camera URLs
- Database connection
- Secret key
- Image storage path

## Usage

1. Access the web interface at `http://localhost:5000`
2. Register a new user account
3. Log in to the system
4. Add shooters, weapons, and shooter groups
5. Start a shooting session
6. Capture and analyze shots

## Camera Setup

Configure your IP cameras to be accessible on your local network. Update the camera URLs in the `.env` file to point to your camera streams.

Example URL formats:
- RTSP: `rtsp://username:password@camera-ip:port/stream`
- HTTP: `http://camera-ip/video`

## License

This project is licensed under the MIT License - see the LICENSE file for details.

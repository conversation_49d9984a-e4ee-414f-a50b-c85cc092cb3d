from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from app import db, login

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), index=True, unique=True)
    email = db.Column(db.String(120), index=True, unique=True)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.<PERSON><PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # Relationships
    shooters = db.relationship('Shooter', backref='coach', lazy='dynamic')

    def __repr__(self):
        return f'<User {self.username}>'

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

@login.user_loader
def load_user(id):
    return User.query.get(int(id))

class ShooterGroup(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), index=True, unique=True)
    description = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    shooters = db.relationship('Shooter', backref='group', lazy='dynamic')

    def __repr__(self):
        return f'<ShooterGroup {self.name}>'

class Weapon(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), index=True)
    type = db.Column(db.String(64), index=True)  # Rifle, Pistol, etc.
    caliber = db.Column(db.String(32))
    description = db.Column(db.String(256))
    photo_path = db.Column(db.String(256))  # Path to the weapon's photo

    # Relationships
    shooting_sessions = db.relationship('ShootingSession', backref='weapon', lazy='dynamic')

    def __repr__(self):
        return f'<Weapon {self.name} ({self.type})>'

    @property
    def photo_url(self):
        if self.photo_path:
            return self.photo_path
        else:
            return 'images/default_weapon.png'

class Shooter(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(64))
    last_name = db.Column(db.String(64))
    army_number = db.Column(db.String(20), index=True, unique=True)  # Military ID/Army Number
    email = db.Column(db.String(120), index=True, unique=True)
    phone = db.Column(db.String(20))
    experience_level = db.Column(db.String(32))  # Beginner, Intermediate, Expert
    photo_path = db.Column(db.String(256))  # Path to the shooter's photo
    camera_id = db.Column(db.Integer)  # ID of the camera assigned to this shooter
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Foreign keys
    group_id = db.Column(db.Integer, db.ForeignKey('shooter_group.id'))
    coach_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relationships
    shooting_sessions = db.relationship('ShootingSession', backref='shooter', lazy='dynamic')

    def __repr__(self):
        return f'<Shooter {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f'{self.first_name} {self.last_name}'

    @property
    def photo_url(self):
        if self.photo_path:
            return self.photo_path
        else:
            return 'images/default_profile.png'

class ShootingSession(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    distance = db.Column(db.Integer)  # Distance in meters
    target_type = db.Column(db.String(64))
    weather_conditions = db.Column(db.String(128))
    notes = db.Column(db.Text)

    # Foreign keys
    shooter_id = db.Column(db.Integer, db.ForeignKey('shooter.id'))
    weapon_id = db.Column(db.Integer, db.ForeignKey('weapon.id'))

    # Relationships
    shots = db.relationship('Shot', backref='session', lazy='dynamic')

    def __repr__(self):
        return f'<ShootingSession {self.id} - {self.date}>'

class Shot(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    image_path = db.Column(db.String(256))  # Path to the captured image
    x_coordinate = db.Column(db.Float)  # X coordinate of the shot on target
    y_coordinate = db.Column(db.Float)  # Y coordinate of the shot on target
    score = db.Column(db.Float)  # Calculated score

    # Foreign keys
    session_id = db.Column(db.Integer, db.ForeignKey('shooting_session.id'))
    camera_id = db.Column(db.Integer)  # ID of the camera that captured the shot

    def __repr__(self):
        return f'<Shot {self.id} - Score: {self.score}>'

class ImageOverlayConfig(db.Model):
    """Configuration for image overlays"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True)
    is_default = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Text settings
    font_family = db.Column(db.String(64), default='Arial')
    font_size = db.Column(db.Integer, default=24)
    font_color = db.Column(db.String(32), default='#FFFFFF')  # White
    font_outline_color = db.Column(db.String(32), default='#000000')  # Black
    font_outline_width = db.Column(db.Integer, default=2)
    text_position = db.Column(db.String(32), default='bottom-left')  # top-left, top-right, bottom-left, bottom-right, center

    # Shot marker settings
    marker_size = db.Column(db.Integer, default=20)
    marker_color = db.Column(db.String(32), default='#FF0000')  # Red
    marker_outline_color = db.Column(db.String(32), default='#FFFFFF')  # White
    marker_outline_width = db.Column(db.Integer, default=2)
    marker_style = db.Column(db.String(32), default='circle')  # circle, cross, square, diamond

    # Scoring zone settings
    show_scoring_zones = db.Column(db.Boolean, default=True)
    scoring_zone_color = db.Column(db.String(32), default='#00FF00')  # Green
    scoring_zone_opacity = db.Column(db.Float, default=0.3)

    # Information display settings
    show_shooter_info = db.Column(db.Boolean, default=True)
    show_session_info = db.Column(db.Boolean, default=True)
    show_timestamp = db.Column(db.Boolean, default=True)
    show_score = db.Column(db.Boolean, default=True)
    show_coordinates = db.Column(db.Boolean, default=False)

    # Watermark settings
    watermark_text = db.Column(db.String(128), default='Armed Forces Shooting Range')
    watermark_opacity = db.Column(db.Float, default=0.3)

    # Background settings
    background_color = db.Column(db.String(32), default='transparent')
    background_opacity = db.Column(db.Float, default=0.0)

    def __repr__(self):
        return f'<ImageOverlayConfig {self.name}>'

/* Custom DataTables Styling for Military Theme */

/* Table Header */
.dataTables_wrapper .table thead th {
    background-color: var(--military-dark-green);
    color: white;
    border-color: var(--military-tan);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 12px 15px;
}

/* Table Body */
.dataTables_wrapper .table tbody td {
    padding: 10px 15px;
    vertical-align: middle;
    border-color: rgba(0, 0, 0, 0.1);
}

/* Striped Rows */
.dataTables_wrapper .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(75, 83, 32, 0.05);
}

/* Hover Effect */
.dataTables_wrapper .table-hover tbody tr:hover {
    background-color: rgba(75, 83, 32, 0.1);
}

/* Search Box */
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid var(--military-tan);
    border-radius: 4px;
    padding: 6px 12px 6px 30px;
    margin-left: 5px;
    background-color: rgba(255, 255, 255, 0.9);
}

.dataTables_wrapper .dataTables_filter input:focus {
    border-color: var(--military-dark-green);
    box-shadow: 0 0 0 0.2rem rgba(26, 58, 26, 0.25);
    outline: 0;
}

/* Search Icon */
.dataTables_wrapper .dataTables_filter label {
    position: relative;
}

.dataTables_wrapper .dataTables_filter label i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--military-dark-green);
}

/* Length Menu */
.dataTables_wrapper .dataTables_length select {
    border: 1px solid var(--military-tan);
    border-radius: 4px;
    padding: 6px 30px 6px 12px;
    background-color: rgba(255, 255, 255, 0.9);
}

.dataTables_wrapper .dataTables_length select:focus {
    border-color: var(--military-dark-green);
    box-shadow: 0 0 0 0.2rem rgba(26, 58, 26, 0.25);
    outline: 0;
}

/* Pagination */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--military-tan);
    background-color: white;
    color: var(--military-dark-green) !important;
    margin: 0 2px;
    border-radius: 4px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: var(--military-tan) !important;
    color: var(--military-dark-green) !important;
    border-color: var(--military-tan);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background-color: var(--military-dark-green) !important;
    color: white !important;
    border-color: var(--military-dark-green);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
    color: #6c757d !important;
    background-color: #f8f9fa !important;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* Info Text */
.dataTables_wrapper .dataTables_info {
    color: #6c757d;
    padding-top: 0.85em;
}

/* Responsive Table */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    background-color: var(--military-dark-green);
    border: 1.5px solid white;
}

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
    background-color: var(--military-tan);
    color: var(--military-dark-green);
}

/* Processing Display */
.dataTables_wrapper .dataTables_processing {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--military-dark-green);
    border: 1px solid var(--military-tan);
    border-radius: 4px;
}

/* Responsive Layout */
@media screen and (max-width: 767px) {
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        text-align: left;
        float: none;
        margin-bottom: 10px;
    }
    
    .dataTables_wrapper .dataTables_filter label {
        display: block;
        margin-bottom: 10px;
    }
    
    .dataTables_wrapper .dataTables_filter input {
        width: 100%;
        margin-left: 0;
        padding-left: 30px;
    }
    
    .dataTables_wrapper .dataTables_filter label i {
        left: 10px;
    }
}

"""Add ImageOverlayConfig model

Revision ID: 9bfd9496a478
Revises: 16b4192e8ef5
Create Date: 2025-05-20 15:06:17.866256

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9bfd9496a478'
down_revision = '16b4192e8ef5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('image_overlay_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=True),
    sa.Column('is_default', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('font_family', sa.String(length=64), nullable=True),
    sa.Column('font_size', sa.Integer(), nullable=True),
    sa.Column('font_color', sa.String(length=32), nullable=True),
    sa.Column('font_outline_color', sa.String(length=32), nullable=True),
    sa.Column('font_outline_width', sa.Integer(), nullable=True),
    sa.Column('text_position', sa.String(length=32), nullable=True),
    sa.Column('marker_size', sa.Integer(), nullable=True),
    sa.Column('marker_color', sa.String(length=32), nullable=True),
    sa.Column('marker_outline_color', sa.String(length=32), nullable=True),
    sa.Column('marker_outline_width', sa.Integer(), nullable=True),
    sa.Column('marker_style', sa.String(length=32), nullable=True),
    sa.Column('show_scoring_zones', sa.Boolean(), nullable=True),
    sa.Column('scoring_zone_color', sa.String(length=32), nullable=True),
    sa.Column('scoring_zone_opacity', sa.Float(), nullable=True),
    sa.Column('show_shooter_info', sa.Boolean(), nullable=True),
    sa.Column('show_session_info', sa.Boolean(), nullable=True),
    sa.Column('show_timestamp', sa.Boolean(), nullable=True),
    sa.Column('show_score', sa.Boolean(), nullable=True),
    sa.Column('show_coordinates', sa.Boolean(), nullable=True),
    sa.Column('watermark_text', sa.String(length=128), nullable=True),
    sa.Column('watermark_opacity', sa.Float(), nullable=True),
    sa.Column('background_color', sa.String(length=32), nullable=True),
    sa.Column('background_opacity', sa.Float(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('image_overlay_config')
    # ### end Alembic commands ###

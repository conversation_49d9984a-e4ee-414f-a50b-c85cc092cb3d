{% extends "base.html" %}

{% block title %}Shooters - Shooting Range Camera System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>Shooters</h1>
        <p class="lead">Manage shooter profiles</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('main.add_shooter') }}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> Add Shooter
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Shooter List</h5>
            </div>
            <div class="card-body">
                {% if shooters %}
                <div class="table-responsive">
                    <table class="table table-hover datatable">
                        <thead>
                            <tr>
                                <th style="width: 80px;">Photo</th>
                                <th>Name</th>
                                <th>Army Number</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Experience</th>
                                <th>Group</th>
                                <th style="width: 100px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for shooter in shooters %}
                            <tr>
                                <td>
                                    <img src="{{ url_for('static', filename=shooter.photo_url) }}" alt="{{ shooter.full_name }}"
                                         class="img-thumbnail rounded-circle profile-image-small">
                                </td>
                                <td>{{ shooter.full_name }}</td>
                                <td>{{ shooter.army_number }}</td>
                                <td>{{ shooter.email }}</td>
                                <td>{{ shooter.phone }}</td>
                                <td>{{ shooter.experience_level }}</td>
                                <td>
                                    {% if shooter.group %}
                                    {{ shooter.group.name }}
                                    {% else %}
                                    <span class="text-muted">None</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('main.shooter', id=shooter.id) }}" class="btn btn-sm btn-info" title="View Shooter Details">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No shooters found. <a href="{{ url_for('main.add_shooter') }}">Add a shooter</a> to get started.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Shooter Groups</h5>
            </div>
            <div class="card-body">
                {% if groups %}
                <div class="row">
                    {% for group in groups %}
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">{{ group.name }}</h5>
                                <p class="card-text">{{ group.description }}</p>
                                <p class="card-text"><small class="text-muted">Members: {{ group.shooters.count() }}</small></p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">No shooter groups found.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGroupModal">
                    <i class="fas fa-plus"></i> Add Group
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Group Modal -->
<div class="modal fade" id="addGroupModal" tabindex="-1" aria-labelledby="addGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addGroupModalLabel">Add Shooter Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('main.add_group') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Group</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

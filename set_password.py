"""
Password Reset Script for Shooting Range Application

This script sets a new password for a specific user.
"""

from app import create_app, db
from app.models import User

def set_password(username, new_password):
    """Set a new password for a specific user"""
    app = create_app()
    
    with app.app_context():
        # Find the user
        user = User.query.filter_by(username=username).first()
        
        if not user:
            print(f"User '{username}' not found.")
            return False
        
        # Set the new password
        user.set_password(new_password)
        db.session.commit()
        
        print(f"Password for {username} has been reset successfully!")
        print(f"You can now log in with the new password: {new_password}")
        return True

if __name__ == "__main__":
    # Set a new password for the user 'smarain'
    # You can change the username and password as needed
    set_password('smarain', 'Password123')

/* Main Styles */
body {
    font-family: 'Roboto', sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.footer {
    margin-top: auto;
}

/* Card Styles */
.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand i {
    margin-right: 5px;
}

/* Dashboard */
.dashboard-stat {
    text-align: center;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.dashboard-stat i {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.dashboard-stat h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.dashboard-stat p {
    font-size: 2rem;
    font-weight: bold;
}

/* Camera View */
.camera-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.camera-feed {
    width: 100%;
    height: auto;
    border-radius: 5px;
    border: 1px solid #ddd;
}

/* Session Details */
.shot-details {
    margin-top: 20px;
}

.shot-image {
    max-width: 100%;
    border-radius: 5px;
    border: 1px solid #ddd;
}

/* Forms */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Buttons */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Tables */
.table th {
    background-color: #f8f9fa;
}

/* List View for Cameras */
.list-view {
    display: block;
}

.list-view .camera-card {
    margin-bottom: 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: 1fr;
    }
}

/* Animation for alerts */
.alert {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

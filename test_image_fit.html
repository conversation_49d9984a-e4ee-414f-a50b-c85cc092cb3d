<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Fit Test</title>
    <style>
        .camera-feed-container {
            position: relative;
            width: 100%;
            height: 200px;
            background-color: #000;
            border-radius: 5px;
            overflow: hidden;
            border: 2px solid red; /* Red border to see container boundaries */
        }

        .camera-feed {
            width: 100%;
            height: 100%;
            object-fit: fill;
            border: none;
            border-radius: 5px;
            display: block;
            background-color: #f8f9fa;
        }

        .test-container {
            max-width: 280px;
            margin: 20px auto;
            padding: 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }

        .card-body {
            padding: 0;
            margin: 0;
            height: 200px;
            overflow: hidden;
        }

        .test-title {
            text-align: center;
            margin: 20px;
            font-family: Arial, sans-serif;
        }

        .info {
            padding: 10px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h3 class="test-title">Image Fit Test</h3>
        <div class="card-body">
            <div class="camera-feed-container">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDI4YmNhIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5URVNUIENBTUVSQTWVNT0iCiAgPC90ZXh0Pgo8L3N2Zz4K" 
                     class="camera-feed" 
                     alt="Test Camera"
                     id="test-camera">
            </div>
        </div>
        <div class="info">
            <strong>Expected:</strong> Blue rectangle should fill entire container with no black borders or gaps.<br>
            <strong>Container:</strong> 280px × 200px<br>
            <strong>Image:</strong> Should stretch to fit exactly
        </div>
    </div>

    <script>
        const testImage = document.getElementById('test-camera');
        
        testImage.onload = function() {
            console.log('Image loaded successfully');
            console.log('Image natural dimensions:', this.naturalWidth, 'x', this.naturalHeight);
            console.log('Image display dimensions:', this.offsetWidth, 'x', this.offsetHeight);
            
            const container = this.parentElement;
            console.log('Container dimensions:', container.offsetWidth, 'x', container.offsetHeight);
        };
        
        // Test with different object-fit values
        function testObjectFit(value) {
            testImage.style.objectFit = value;
            console.log('Testing object-fit:', value);
        }
        
        // Add buttons to test different object-fit values
        const infoDiv = document.querySelector('.info');
        const buttonContainer = document.createElement('div');
        buttonContainer.style.marginTop = '10px';
        
        ['fill', 'cover', 'contain', 'scale-down', 'none'].forEach(value => {
            const button = document.createElement('button');
            button.textContent = value;
            button.style.margin = '2px';
            button.style.padding = '4px 8px';
            button.style.fontSize = '10px';
            button.onclick = () => testObjectFit(value);
            buttonContainer.appendChild(button);
        });
        
        infoDiv.appendChild(buttonContainer);
    </script>
</body>
</html>

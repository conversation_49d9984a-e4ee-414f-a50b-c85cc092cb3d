import os
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Camera settings
    CAMERA_COUNT = int(os.environ.get('CAMERA_COUNT') or '8')
    CAMERA_URLS = []
    
    # Load camera URLs from environment variables
    for i in range(1, CAMERA_COUNT + 1):
        url = os.environ.get(f'CAMERA_URL_{i}')
        if url:
            CAMERA_URLS.append(url)
        else:
            # Default placeholder URLs if not specified
            CAMERA_URLS.append(f'http://camera{i}.local/video')
    
    # Image storage
    IMAGE_STORAGE_PATH = os.environ.get('IMAGE_STORAGE_PATH') or os.path.join(basedir, 'app/static/images/shots')
    
    # Ensure the image storage directory exists
    os.makedirs(IMAGE_STORAGE_PATH, exist_ok=True)

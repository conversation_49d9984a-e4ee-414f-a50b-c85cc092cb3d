{% extends "base.html" %}

{% block title %}Camera Settings - Armed Forces Shooting Range{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="military-badge">SYSTEM CONFIGURATION</div>
        <h1>CAMERA SETTINGS</h1>
        <p class="lead">Configure IP camera addresses and connection settings</p>
        <div class="military-divider"></div>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('camera_bp.view_cameras') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> BACK TO CAMERAS
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        {% endif %}
        {% endwith %}
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">CAMERA CONFIGURATION</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('camera_bp.save_camera_settings') }}" method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle me-2"></i> Camera URL Format</h5>
                                <p>Use one of the following formats depending on your camera type:</p>
                                <ul>
                                    <li><strong>IP Cameras:</strong> http://*************:8080/video</li>
                                    <li><strong>RTSP Stream:</strong> rtsp://username:password@*************:554/stream</li>
                                    <li><strong>Local Webcam:</strong> 0 (for the first webcam)</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-warning">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i> Important Notes</h5>
                                <p>Make sure your cameras are:</p>
                                <ul>
                                    <li>Connected to the same network as this server</li>
                                    <li>Have stable IP addresses (use DHCP reservations or static IPs)</li>
                                    <li>Accessible from this server (check firewall settings)</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="military-divider"></div>
                    
                    <div class="row">
                        {% for i in range(1, camera_count + 1) %}
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header {% if camera_status[i]['connected'] %}bg-success{% else %}bg-secondary{% endif %} text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-video me-2"></i> CAMERA {{ i }}
                                        {% if camera_status[i]['connected'] %}
                                        <span class="badge bg-light text-success float-end">ONLINE</span>
                                        {% else %}
                                        <span class="badge bg-light text-secondary float-end">OFFLINE</span>
                                        {% endif %}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="camera_url_{{ i }}" class="form-label">Camera URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-link"></i></span>
                                            <input type="text" class="form-control" id="camera_url_{{ i }}" name="camera_url_{{ i }}" 
                                                value="{{ camera_status[i]['url'] }}" placeholder="http://camera-ip-address/video">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="camera_name_{{ i }}" class="form-label">Camera Name (Optional)</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                            <input type="text" class="form-control" id="camera_name_{{ i }}" name="camera_name_{{ i }}" 
                                                value="{{ camera_names[i] }}" placeholder="e.g., Lane 1 Camera">
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-outline-primary test-connection-btn" data-camera-id="{{ i }}">
                                            <i class="fas fa-plug me-2"></i> TEST CONNECTION
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="military-divider"></div>
                    
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i> SAVE CAMERA SETTINGS
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Test camera connection
        $('.test-connection-btn').click(function() {
            const cameraId = $(this).data('camera-id');
            const cameraUrl = $('#camera_url_' + cameraId).val();
            
            if (!cameraUrl) {
                alert('Please enter a camera URL first.');
                return;
            }
            
            // Show loading state
            const $btn = $(this);
            const originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-2"></i> TESTING...');
            $btn.prop('disabled', true);
            
            // Send test request
            $.ajax({
                url: '/camera/test-connection',
                type: 'POST',
                data: {
                    camera_id: cameraId,
                    camera_url: cameraUrl
                },
                success: function(response) {
                    if (response.success) {
                        alert('Connection successful! Camera is accessible.');
                    } else {
                        alert('Connection failed: ' + response.error);
                    }
                },
                error: function() {
                    alert('Error testing connection. Please try again.');
                },
                complete: function() {
                    // Restore button state
                    $btn.html(originalText);
                    $btn.prop('disabled', false);
                }
            });
        });
    });
</script>
{% endblock %}
